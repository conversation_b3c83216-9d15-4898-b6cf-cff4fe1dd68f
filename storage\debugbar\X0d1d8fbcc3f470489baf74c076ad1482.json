{"__meta": {"id": "X0d1d8fbcc3f470489baf74c076ad1482", "datetime": "2025-07-07 21:43:32", "utime": 1751924612.748098, "method": "GET", "uri": "/mazar/public/?set_lang=fr", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751924612.306378, "end": 1751924612.748151, "duration": 0.4417731761932373, "duration_str": "442ms", "measures": [{"label": "Booting", "start": 1751924612.306378, "relative_start": 0, "end": 1751924612.695608, "relative_end": 1751924612.695608, "duration": 0.38923001289367676, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751924612.695627, "relative_start": 0.3892490863800049, "end": 1751924612.748156, "relative_end": 5.0067901611328125e-06, "duration": 0.052529096603393555, "duration_str": "52.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5059600, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=31\" onclick=\"\">app/Http/Controllers/HomeController.php:31-61</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public?set_lang=fr\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "bc_current_currency": "tnd", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-78341869 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-78341869\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-619532737 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>set_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619532737\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1134634610 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1134634610\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/mazar/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IkY5WkhmUjQzSzhsTnpIMnFpaE1DN2c9PSIsInZhbHVlIjoiSkYzTVlpZkN1V0R5V09SZ1loc1J2N2pVQ1dEYmJEekhDalhGZ2xFcW94RlFpbWxlVFpzME10ZnUvSXVkWEd4a3FDenNBQTBtRWROUlFna3JCWVZWOHdNYWxiS2s3YnZXaUgzaTQ3QkdycFcvTkF0MCtGdVNZM2RQRFpxY2paanoiLCJtYWMiOiI3NjI2ZTI3NGRiOWY0ZDFhNTQ2NDI4ZmJhYmJjMjg4YTAwNDM0ZjA5MzFiYTFkYTUxOGI4YmY4YzhmNzA0YWMyIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6InViS1QvTngrdkU5ZnVsQ3dUakxhSVE9PSIsInZhbHVlIjoiYUJWaEIwMlpNOUlqQ0pnTFd4MDBDVkdTYzRhb0p0T2dkMDBrTkRNV1oweEIvZW1TZ0hFZ0VadTNvT1FRbTRtamdEQkhYcGhWcWlXY2dhQU9IUzJyNnJkYVRRM0duQlpYMEdJZitSejFUOXlwdzFoUXhOYml0RkZ5eGRZS1E5SFQiLCJtYWMiOiI2YjYxNTQ0ZDQyMWRhMDkxZTIzN2EyODY2NGQxODNkYjdlMWUzYjRiNDUzNWJjM2Y5YTUyOGUxYTVmY2ViODdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:43:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost/mazar/public/fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNRZ3RTQUhUN2NvMmd1N3VIK0xsZWc9PSIsInZhbHVlIjoibkF2bmRyeFljUVhoZzhCRSs3cXcvU29HdnVFSEYxc3RDYXpZSno2blBRS0xwSlFrVURPK0grVXBsb2RYS0dDcVlIOE82bHBDcElQKzJBQTBGTmdGTnRDWDhPb3VxMUp4V0p1Z3ZtRVlLUGFhYTJWWUsyeG1mSDg1R0tlUG5IeFMiLCJtYWMiOiI3MTU1ZWRiZjEwZTZkMjQwNDIwMGRkZTM2YzAyMTgzMTI2ODI5Mjk2NDFhZjc2MjkxNjY5YWE2NTE2YzU5ZDU3IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:43:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IlRPYW1NNDhXWEwyTWtIQzlyZFowOEE9PSIsInZhbHVlIjoiRENwMzBXOVR3bEFvTEhlMzZ5NnVnSDcvbXo0MjF1YVloMEFjVGRwcmtUYXRqaXJzM0hSdkhUWjQ3NERES283dThlV3R4dnJsUW1VOEdwS1dsbmJKZGkzN2YzZWZMS3JnR01zbllXdHNGYUhobXJydThZT3g3WXE3SUIrN29abDIiLCJtYWMiOiJlYWFmZjhhYzRhMmQzYzA4NmYzZTJiNWRlYTU0M2Q3ZWZiYjFlNDFkMDIzNDI3OTljZGQxZTVkOGE0ZDA2YTljIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:43:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNRZ3RTQUhUN2NvMmd1N3VIK0xsZWc9PSIsInZhbHVlIjoibkF2bmRyeFljUVhoZzhCRSs3cXcvU29HdnVFSEYxc3RDYXpZSno2blBRS0xwSlFrVURPK0grVXBsb2RYS0dDcVlIOE82bHBDcElQKzJBQTBGTmdGTnRDWDhPb3VxMUp4V0p1Z3ZtRVlLUGFhYTJWWUsyeG1mSDg1R0tlUG5IeFMiLCJtYWMiOiI3MTU1ZWRiZjEwZTZkMjQwNDIwMGRkZTM2YzAyMTgzMTI2ODI5Mjk2NDFhZjc2MjkxNjY5YWE2NTE2YzU5ZDU3IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:43:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IlRPYW1NNDhXWEwyTWtIQzlyZFowOEE9PSIsInZhbHVlIjoiRENwMzBXOVR3bEFvTEhlMzZ5NnVnSDcvbXo0MjF1YVloMEFjVGRwcmtUYXRqaXJzM0hSdkhUWjQ3NERES283dThlV3R4dnJsUW1VOEdwS1dsbmJKZGkzN2YzZWZMS3JnR01zbllXdHNGYUhobXJydThZT3g3WXE3SUIrN29abDIiLCJtYWMiOiJlYWFmZjhhYzRhMmQzYzA4NmYzZTJiNWRlYTU0M2Q3ZWZiYjFlNDFkMDIzNDI3OTljZGQxZTVkOGE0ZDA2YTljIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:43:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1500852790 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/mazar/public?set_lang=fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">tnd</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500852790\", {\"maxDepth\":0})</script>\n"}}