{"__meta": {"id": "Xbb497bcce4686380c1e045b957279f9f", "datetime": "2025-07-07 21:42:50", "utime": 1751924570.88039, "method": "GET", "uri": "/mazar/public/", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[21:42:44] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\wamp64\\www\\mazar\\app\\BaseModel.php on line 206", "message_html": null, "is_string": false, "label": "warning", "time": **********.521494, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751924563.213927, "end": 1751924570.880495, "duration": 7.666568040847778, "duration_str": "7.67s", "measures": [{"label": "Booting", "start": 1751924563.213927, "relative_start": 0, "end": **********.35997, "relative_end": **********.35997, "duration": 1.146043062210083, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.360015, "relative_start": 1.146087884902954, "end": 1751924570.880504, "relative_end": 8.821487426757812e-06, "duration": 6.520488977432251, "duration_str": "6.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 9537816, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 104, "templates": [{"name": "1x Page::frontend.detail", "param_count": null, "params": [], "start": **********.522942, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.phpPage::frontend.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FPage%2FViews%2Ffrontend%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "Page::frontend.detail"}, {"name": "1x Template::frontend.blocks.form-search-all-service.index", "param_count": null, "params": [], "start": **********.599191, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/form-search-all-service/index.blade.phpTemplate::frontend.blocks.form-search-all-service.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fform-search-all-service%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.form-search-all-service.index"}, {"name": "1x Template::frontend.blocks.form-search-all-service.style-normal", "param_count": null, "params": [], "start": **********.600239, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/form-search-all-service/style-normal.blade.phpTemplate::frontend.blocks.form-search-all-service.style-normal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fform-search-all-service%2Fstyle-normal.blade.php&line=1", "ajax": false, "filename": "style-normal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.form-search-all-service.style-normal"}, {"name": "1x Template::frontend.blocks.form-search-all-service.form-search", "param_count": null, "params": [], "start": **********.604279, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/form-search-all-service/form-search.blade.phpTemplate::frontend.blocks.form-search-all-service.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fform-search-all-service%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.form-search-all-service.form-search"}, {"name": "1x Hotel::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.672389, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/form-search.blade.phpHotel::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.form-search"}, {"name": "1x Hotel::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.67531, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/fields/location.blade.phpHotel::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.fields.location"}, {"name": "1x Hotel::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.700399, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/fields/date.blade.phpHotel::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.fields.date"}, {"name": "1x Hotel::frontend.layouts.search.fields.guests", "param_count": null, "params": [], "start": **********.704819, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/fields/guests.blade.phpHotel::frontend.layouts.search.fields.guests", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fguests.blade.php&line=1", "ajax": false, "filename": "guests.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.fields.guests"}, {"name": "1x Space::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.720916, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/form-search.blade.phpSpace::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.form-search"}, {"name": "1x Space::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.72318, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/fields/location.blade.phpSpace::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.fields.location"}, {"name": "1x Space::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.75219, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/fields/date.blade.phpSpace::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.fields.date"}, {"name": "1x Space::frontend.layouts.search.fields.guests", "param_count": null, "params": [], "start": **********.756665, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/fields/guests.blade.phpSpace::frontend.layouts.search.fields.guests", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fguests.blade.php&line=1", "ajax": false, "filename": "guests.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.fields.guests"}, {"name": "1x Tour::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.769207, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/search/form-search.blade.phpTour::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.search.form-search"}, {"name": "1x Tour::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.771436, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/search/fields/location.blade.phpTour::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.search.fields.location"}, {"name": "1x Tour::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.792449, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/search/fields/date.blade.phpTour::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.search.fields.date"}, {"name": "1x Car::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.803614, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/form-search.blade.phpCar::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.search.form-search"}, {"name": "1x Car::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.80597, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/fields/location.blade.phpCar::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.search.fields.location"}, {"name": "1x Car::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.826592, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/fields/date.blade.phpCar::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.search.fields.date"}, {"name": "1x Event::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.83813, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/form-search.blade.phpEvent::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.layouts.search.form-search"}, {"name": "1x Event::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.845195, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/fields/location.blade.phpEvent::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.layouts.search.fields.location"}, {"name": "1x Event::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.884137, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/fields/date.blade.phpEvent::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.layouts.search.fields.date"}, {"name": "1x Flight::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.895484, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/form-search.blade.phpFlight::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Flight::frontend.layouts.search.form-search"}, {"name": "1x Flight::frontend.layouts.search.fields.from-where", "param_count": null, "params": [], "start": **********.898442, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/fields/from-where.blade.phpFlight::frontend.layouts.search.fields.from-where", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Ffrom-where.blade.php&line=1", "ajax": false, "filename": "from-where.blade.php", "line": "?"}, "render_count": 1, "name_original": "Flight::frontend.layouts.search.fields.from-where"}, {"name": "2x Flight::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.899615, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/fields/location.blade.phpFlight::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 2, "name_original": "Flight::frontend.layouts.search.fields.location"}, {"name": "1x Flight::frontend.layouts.search.fields.to-where", "param_count": null, "params": [], "start": **********.919526, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/fields/to-where.blade.phpFlight::frontend.layouts.search.fields.to-where", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fto-where.blade.php&line=1", "ajax": false, "filename": "to-where.blade.php", "line": "?"}, "render_count": 1, "name_original": "Flight::frontend.layouts.search.fields.to-where"}, {"name": "1x Flight::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.936226, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/fields/date.blade.phpFlight::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Flight::frontend.layouts.search.fields.date"}, {"name": "1x Flight::frontend.layouts.search.fields.seat_type", "param_count": null, "params": [], "start": **********.938888, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/fields/seat_type.blade.phpFlight::frontend.layouts.search.fields.seat_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fseat_type.blade.php&line=1", "ajax": false, "filename": "seat_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "Flight::frontend.layouts.search.fields.seat_type"}, {"name": "1x Boat::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": **********.962049, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Boat/Views/frontend/layouts/search/form-search.blade.phpBoat::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FBoat%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Boat::frontend.layouts.search.form-search"}, {"name": "1x Boat::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": **********.965259, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Boat/Views/frontend/layouts/search/fields/location.blade.phpBoat::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FBoat%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Boat::frontend.layouts.search.fields.location"}, {"name": "1x Boat::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": **********.989759, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Boat/Views/frontend/layouts/search/fields/date.blade.phpBoat::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FBoat%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Boat::frontend.layouts.search.fields.date"}, {"name": "1x Template::frontend.blocks.offer-block.index", "param_count": null, "params": [], "start": **********.992879, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/offer-block/index.blade.phpTemplate::frontend.blocks.offer-block.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Foffer-block%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.offer-block.index"}, {"name": "1x Hotel::frontend.blocks.list-hotel.index", "param_count": null, "params": [], "start": 1751924565.081931, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/blocks/list-hotel/index.blade.phpHotel::frontend.blocks.list-hotel.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Fblocks%2Flist-hotel%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.blocks.list-hotel.index"}, {"name": "4x Hotel::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1751924565.083069, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/loop-grid.blade.phpHotel::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Hotel::frontend.layouts.search.loop-grid"}, {"name": "1x Location::frontend.blocks.list-locations.index", "param_count": null, "params": [], "start": 1751924565.355204, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/index.blade.phpLocation::frontend.blocks.list-locations.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLocation%2FViews%2Ffrontend%2Fblocks%2Flist-locations%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Location::frontend.blocks.list-locations.index"}, {"name": "6x Location::frontend.blocks.list-locations.loop", "param_count": null, "params": [], "start": 1751924565.356313, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.phpLocation::frontend.blocks.list-locations.loop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLocation%2FViews%2Ffrontend%2Fblocks%2Flist-locations%2Floop.blade.php&line=1", "ajax": false, "filename": "loop.blade.php", "line": "?"}, "render_count": 6, "name_original": "Location::frontend.blocks.list-locations.loop"}, {"name": "1x Tour::frontend.blocks.list-tour.index", "param_count": null, "params": [], "start": 1751924565.87944, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/blocks/list-tour/index.blade.phpTour::frontend.blocks.list-tour.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Fblocks%2Flist-tour%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.blocks.list-tour.index"}, {"name": "1x Tour::frontend.blocks.list-tour.style-normal", "param_count": null, "params": [], "start": 1751924565.880421, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/blocks/list-tour/style-normal.blade.phpTour::frontend.blocks.list-tour.style-normal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Fblocks%2Flist-tour%2Fstyle-normal.blade.php&line=1", "ajax": false, "filename": "style-normal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.blocks.list-tour.style-normal"}, {"name": "6x Tour::frontend.blocks.list-tour.loop-box-shadow", "param_count": null, "params": [], "start": 1751924565.881396, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/blocks/list-tour/loop-box-shadow.blade.phpTour::frontend.blocks.list-tour.loop-box-shadow", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Fblocks%2Flist-tour%2Floop-box-shadow.blade.php&line=1", "ajax": false, "filename": "loop-box-shadow.blade.php", "line": "?"}, "render_count": 6, "name_original": "Tour::frontend.blocks.list-tour.loop-box-shadow"}, {"name": "1x Space::frontend.blocks.list-space.index", "param_count": null, "params": [], "start": 1751924566.657517, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/blocks/list-space/index.blade.phpSpace::frontend.blocks.list-space.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Fblocks%2Flist-space%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.blocks.list-space.index"}, {"name": "4x Space::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1751924566.658759, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/loop-grid.blade.phpSpace::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Space::frontend.layouts.search.loop-grid"}, {"name": "1x Car::frontend.blocks.list-car.index", "param_count": null, "params": [], "start": 1751924567.086492, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/blocks/list-car/index.blade.phpCar::frontend.blocks.list-car.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Fblocks%2Flist-car%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.blocks.list-car.index"}, {"name": "8x Car::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1751924567.08797, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/loop-grid.blade.phpCar::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 8, "name_original": "Car::frontend.layouts.search.loop-grid"}, {"name": "1x Event::frontend.blocks.list-event.index", "param_count": null, "params": [], "start": 1751924567.774757, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/blocks/list-event/index.blade.phpEvent::frontend.blocks.list-event.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Fblocks%2Flist-event%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.blocks.list-event.index"}, {"name": "4x Event::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1751924567.776814, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/loop-grid.blade.phpEvent::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Event::frontend.layouts.search.loop-grid"}, {"name": "1x Boat::frontend.blocks.list-boat.index", "param_count": null, "params": [], "start": 1751924568.539465, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Boat/Views/frontend/blocks/list-boat/index.blade.phpBoat::frontend.blocks.list-boat.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FBoat%2FViews%2Ffrontend%2Fblocks%2Flist-boat%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Boat::frontend.blocks.list-boat.index"}, {"name": "4x Boat::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1751924568.541668, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Boat/Views/frontend/layouts/search/loop-grid.blade.phpBoat::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FBoat%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Boat::frontend.layouts.search.loop-grid"}, {"name": "1x News::frontend.blocks.list-news.index", "param_count": null, "params": [], "start": 1751924568.975987, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/index.blade.phpNews::frontend.blocks.list-news.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FNews%2FViews%2Ffrontend%2Fblocks%2Flist-news%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "News::frontend.blocks.list-news.index"}, {"name": "6x News::frontend.blocks.list-news.loop", "param_count": null, "params": [], "start": 1751924568.9783, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.phpNews::frontend.blocks.list-news.loop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FNews%2FViews%2Ffrontend%2Fblocks%2Flist-news%2Floop.blade.php&line=1", "ajax": false, "filename": "loop.blade.php", "line": "?"}, "render_count": 6, "name_original": "News::frontend.blocks.list-news.loop"}, {"name": "1x Template::frontend.blocks.call-to-action.index", "param_count": null, "params": [], "start": 1751924569.182944, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/call-to-action/index.blade.phpTemplate::frontend.blocks.call-to-action.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fcall-to-action%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.call-to-action.index"}, {"name": "1x Template::frontend.blocks.call-to-action.style-normal", "param_count": null, "params": [], "start": 1751924569.185312, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/call-to-action/style-normal.blade.phpTemplate::frontend.blocks.call-to-action.style-normal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fcall-to-action%2Fstyle-normal.blade.php&line=1", "ajax": false, "filename": "style-normal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.call-to-action.style-normal"}, {"name": "1x Template::frontend.blocks.testimonial.index", "param_count": null, "params": [], "start": 1751924569.188053, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/testimonial/index.blade.phpTemplate::frontend.blocks.testimonial.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Ftestimonial%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.testimonial.index"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": 1751924569.205624, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x Layout::app", "param_count": null, "params": [], "start": 1751924569.209217, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/app.blade.phpLayout::app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::app"}, {"name": "1x Layout::parts.favicon", "param_count": null, "params": [], "start": 1751924569.213939, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/favicon.blade.phpLayout::parts.favicon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffavicon.blade.php&line=1", "ajax": false, "filename": "favicon.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.favicon"}, {"name": "1x Layout::parts.seo-meta", "param_count": null, "params": [], "start": 1751924569.220023, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/seo-meta.blade.phpLayout::parts.seo-meta", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fseo-meta.blade.php&line=1", "ajax": false, "filename": "seo-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.seo-meta"}, {"name": "1x Layout::parts.global-script", "param_count": null, "params": [], "start": 1751924569.232636, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/global-script.blade.phpLayout::parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.global-script"}, {"name": "1x Layout::parts.topbar", "param_count": null, "params": [], "start": 1751924569.397975, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/topbar.blade.phpLayout::parts.topbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Ftopbar.blade.php&line=1", "ajax": false, "filename": "topbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.topbar"}, {"name": "2x Core::frontend.currency-switcher", "param_count": null, "params": [], "start": 1751924569.453412, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Core/Views/frontend/currency-switcher.blade.phpCore::frontend.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCore%2FViews%2Ffrontend%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Core::frontend.currency-switcher"}, {"name": "2x Language::frontend.switcher", "param_count": null, "params": [], "start": 1751924569.576896, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Language/Views/frontend/switcher.blade.phpLanguage::frontend.switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLanguage%2FViews%2Ffrontend%2Fswitcher.blade.php&line=1", "ajax": false, "filename": "switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Language::frontend.switcher"}, {"name": "1x Layout::parts.notification", "param_count": null, "params": [], "start": 1751924569.621062, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/notification.blade.phpLayout::parts.notification", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.notification"}, {"name": "1x Layout::parts.header", "param_count": null, "params": [], "start": 1751924569.816196, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/header.blade.phpLayout::parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.header"}, {"name": "1x Layout::parts.footer", "param_count": null, "params": [], "start": 1751924570.241431, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/footer.blade.phpLayout::parts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.footer"}, {"name": "1x Layout::parts.login-register-modal", "param_count": null, "params": [], "start": 1751924570.285598, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/login-register-modal.blade.phpLayout::parts.login-register-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Flogin-register-modal.blade.php&line=1", "ajax": false, "filename": "login-register-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.login-register-modal"}, {"name": "1x Layout::auth.login-form", "param_count": null, "params": [], "start": 1751924570.297957, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/login-form.blade.phpLayout::auth.login-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Flogin-form.blade.php&line=1", "ajax": false, "filename": "login-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.login-form"}, {"name": "1x Layout::auth.register-form", "param_count": null, "params": [], "start": 1751924570.312503, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/register-form.blade.phpLayout::auth.register-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Fregister-form.blade.php&line=1", "ajax": false, "filename": "register-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.register-form"}, {"name": "1x Popup::frontend.popup", "param_count": null, "params": [], "start": 1751924570.324978, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Popup/Views/frontend/popup.blade.phpPopup::frontend.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FPopup%2FViews%2Ffrontend%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "Popup::frontend.popup"}, {"name": "1x demo_script", "param_count": null, "params": [], "start": 1751924570.36932, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/demo_script.blade.phpdemo_script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fdemo_script.blade.php&line=1", "ajax": false, "filename": "demo_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "demo_script"}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=31\" onclick=\"\">app/Http/Controllers/HomeController.php:31-61</a>"}, "queries": {"nb_statements": 81, "nb_failed_statements": 0, "accumulated_duration": 0.11937000000000002, "accumulated_duration_str": "119ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.464238, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.477115, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `core_pages` where `id` = '1' and `status` = 'publish' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4911299, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:34", "source": "app/Http/Controllers/HomeController.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=34", "ajax": false, "filename": "HomeController.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `core_page_translations` where `core_page_translations`.`origin_id` = 1 and `core_page_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeController.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.500446, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_seo` where `object_id` = 1 and `object_model` = 'page' limit 1", "type": "query", "params": [], "bindings": ["1", "page"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 212}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.509537, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:199", "source": "app/BaseModel.php:199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=199", "ajax": false, "filename": "BaseModel.php", "line": "199"}, "connection": "mazar_travel"}, {"sql": "select * from `core_templates` where `core_templates`.`id` = 1 and `core_templates`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 79}, {"index": 22, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.524707, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Page.php:79", "source": "modules/Page/Models/Page.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FPage%2FModels%2FPage.php&line=79", "ajax": false, "filename": "Page.php", "line": "79"}, "connection": "mazar_travel"}, {"sql": "select * from `core_template_translations` where `core_template_translations`.`origin_id` = 1 and `core_template_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 81}, {"index": 23, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.532624, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `name` asc limit 1000", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 129}, {"index": 18, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 21, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 22, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.555922, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:129", "source": "modules/Template/Blocks/FormSearchAllService.php:129", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=129", "ajax": false, "filename": "FormSearchAllService.php", "line": "129"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 129}, {"index": 23, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 26, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 27, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.568748, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:129", "source": "modules/Template/Blocks/FormSearchAllService.php:129", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=129", "ajax": false, "filename": "FormSearchAllService.php", "line": "129"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_category` where `status` = 'publish' and `bravo_tour_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 130}, {"index": 18, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 21, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 22, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5799181, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:130", "source": "modules/Template/Blocks/FormSearchAllService.php:130", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=130", "ajax": false, "filename": "FormSearchAllService.php", "line": "130"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_category_translations` where `locale` = 'en' and `bravo_tour_category_translations`.`origin_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 130}, {"index": 23, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 26, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 27, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.591269, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:130", "source": "modules/Template/Blocks/FormSearchAllService.php:130", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=130", "ajax": false, "filename": "FormSearchAllService.php", "line": "130"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_seat_type` where `bravo_seat_type`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 18, "namespace": "view", "name": "Flight::frontend.layouts.search.fields.seat_type", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Flight/Views/frontend/layouts/search/fields/seat_type.blade.php", "line": 2}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.94059, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Flight::frontend.layouts.search.fields.seat_type:2", "source": "view::Flight::frontend.layouts.search.fields.seat_type:2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FFlight%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fseat_type.blade.php&line=2", "ajax": false, "filename": "seat_type.blade.php", "line": "2"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_hotels`.* from `bravo_hotels` where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null group by `bravo_hotels`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 17, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.001713, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select `bravo_hotels`.* from `bravo_hotels` where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null group by `bravo_hotels`.`id` order by `bravo_hotels`.`id` asc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 17, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.010323, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 22, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.019928, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 27, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.0274322, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'hotel' and `user_id` = 7 and `user_wishlist`.`object_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": ["hotel", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 22, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.035429, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_hotel_translations` where `locale` = 'en' and `bravo_hotel_translations`.`origin_id` in (1, 2, 3, 4) and `bravo_hotel_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 22, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.045206, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select `bravo_terms`.*, `bravo_hotel_term`.`target_id` as `laravel_through_key` from `bravo_terms` inner join `bravo_hotel_term` on `bravo_hotel_term`.`term_id` = `bravo_terms`.`id` where `bravo_terms`.`attr_id` = '6' and `bravo_hotel_term`.`target_id` in (1, 2, 3, 4) and `bravo_terms`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6"], "hints": [], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.057376, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_terms_translations` where `locale` = 'en' and `bravo_terms_translations`.`origin_id` in (42, 43, 44, 45, 46, 47, 48)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 26, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 29, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 32, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 33, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.0721118, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `id` asc limit 6", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 168}, {"index": 16, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 22, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 23, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.338052, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ListLocations.php:168", "source": "modules/Location/Blocks/ListLocations.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FBlocks%2FListLocations.php&line=168", "ajax": false, "filename": "ListLocations.php", "line": "168"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 168}, {"index": 21, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 132}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.347706, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ListLocations.php:168", "source": "modules/Location/Blocks/ListLocations.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FBlocks%2FListLocations.php&line=168", "ajax": false, "filename": "ListLocations.php", "line": "168"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.369575, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.393696, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.418112, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.446471, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.4721498, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.4972138, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.527264, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.551614, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.576638, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.602716, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.6270301, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.6437511, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.675099, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.690183, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.725757, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.755769, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.771655, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924565.7871869, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_category` where (0 = 1 or 0 = 1 and `status` = 'publish') and `bravo_tour_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 1003}, {"index": 16, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 176}, {"index": 17, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}], "start": 1751924565.820261, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Tour.php:1003", "source": "modules/Tour/Models/Tour.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=1003", "ajax": false, "filename": "Tour.php", "line": "1003"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_tours`.* from `bravo_tours` where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null group by `bravo_tours`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 17, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.82896, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select `bravo_tours`.* from `bravo_tours` where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null group by `bravo_tours`.`id` order by `bravo_tours`.`id` asc limit 6 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 17, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.83809, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1, 2) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 22, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.848326, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 27, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.856535, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'tour' and `user_id` = 7 and `user_wishlist`.`object_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["tour", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 22, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.863359, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_translations` where `locale` = 'en' and `bravo_tour_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 22, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924565.872034, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_spaces`.* from `bravo_spaces` where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null group by `bravo_spaces`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 17, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924566.589592, "duration": 0.01091, "duration_str": "10.91ms", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select `bravo_spaces`.* from `bravo_spaces` where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null group by `bravo_spaces`.`id` order by `bravo_spaces`.`id` desc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 17, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924566.605847, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 22, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924566.618099, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 27, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924566.624367, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'space' and `user_id` = 7 and `user_wishlist`.`object_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": ["space", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 22, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924566.63444, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_space_translations` where `locale` = 'en' and `bravo_space_translations`.`origin_id` in (8, 9, 10, 11) and `bravo_space_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 22, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924566.643533, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_cars`.* from `bravo_cars` where `bravo_cars`.`status` = 'publish' and `bravo_cars`.`deleted_at` is null group by `bravo_cars`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 17, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.0203528, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select `bravo_cars`.* from `bravo_cars` where `bravo_cars`.`status` = 'publish' and `bravo_cars`.`deleted_at` is null group by `bravo_cars`.`id` order by `bravo_cars`.`id` desc limit 8 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 17, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.03128, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1, 3, 6, 7, 8) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 22, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.043562, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 3, 6, 7, 8)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 27, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.053993, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'car' and `user_id` = 7 and `user_wishlist`.`object_id` in (6, 7, 8, 9, 10, 11, 12, 13)", "type": "query", "params": [], "bindings": ["car", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 22, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.064267, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_car_translations` where `locale` = 'en' and `bravo_car_translations`.`origin_id` in (6, 7, 8, 9, 10, 11, 12, 13) and `bravo_car_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 22, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.076792, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_events`.* from `bravo_events` where `bravo_events`.`status` = 'publish' and `bravo_events`.`deleted_at` is null group by `bravo_events`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 17, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.679523, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select `bravo_events`.* from `bravo_events` where `bravo_events`.`status` = 'publish' and `bravo_events`.`deleted_at` is null group by `bravo_events`.`id` order by `bravo_events`.`is_featured` desc, `bravo_events`.`id` desc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 17, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.695881, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (2, 4, 6, 7) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 22, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.71491, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (2, 4, 6, 7)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 27, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.730999, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'event' and `user_id` = 7 and `user_wishlist`.`object_id` in (6, 9, 11, 12)", "type": "query", "params": [], "bindings": ["event", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 22, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.7451432, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_event_translations` where `locale` = 'en' and `bravo_event_translations`.`origin_id` in (6, 9, 11, 12) and `bravo_event_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 22, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924567.760966, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_boats`.* from `bravo_boats` where `bravo_boats`.`status` = 'publish' and `bravo_boats`.`deleted_at` is null group by `bravo_boats`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 152}, {"index": 17, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 131}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.45115, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "ListBoat.php:152", "source": "modules/Boat/Blocks/ListBoat.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FBlocks%2FListBoat.php&line=152", "ajax": false, "filename": "ListBoat.php", "line": "152"}, "connection": "mazar_travel"}, {"sql": "select `bravo_boats`.* from `bravo_boats` where `bravo_boats`.`status` = 'publish' and `bravo_boats`.`deleted_at` is null group by `bravo_boats`.`id` order by `bravo_boats`.`id` asc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 152}, {"index": 17, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 131}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.466738, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "ListBoat.php:152", "source": "modules/Boat/Blocks/ListBoat.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FBlocks%2FListBoat.php&line=152", "ajax": false, "filename": "ListBoat.php", "line": "152"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1, 3) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 152}, {"index": 22, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 131}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.483868, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "ListBoat.php:152", "source": "modules/Boat/Blocks/ListBoat.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FBlocks%2FListBoat.php&line=152", "ajax": false, "filename": "ListBoat.php", "line": "152"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 3)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 152}, {"index": 27, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 131}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.4979231, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ListBoat.php:152", "source": "modules/Boat/Blocks/ListBoat.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FBlocks%2FListBoat.php&line=152", "ajax": false, "filename": "ListBoat.php", "line": "152"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'boat' and `user_id` = 7 and `user_wishlist`.`object_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": ["boat", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 152}, {"index": 22, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 131}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.510932, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ListBoat.php:152", "source": "modules/Boat/Blocks/ListBoat.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FBlocks%2FListBoat.php&line=152", "ajax": false, "filename": "ListBoat.php", "line": "152"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_boat_translations` where `locale` = 'en' and `bravo_boat_translations`.`origin_id` in (1, 2, 3, 4) and `bravo_boat_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 152}, {"index": 22, "namespace": null, "name": "modules/Boat/Blocks/ListBoat.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Boat\\Blocks\\ListBoat.php", "line": 131}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.526157, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "ListBoat.php:152", "source": "modules/Boat/Blocks/ListBoat.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FBlocks%2FListBoat.php&line=152", "ajax": false, "filename": "ListBoat.php", "line": "152"}, "connection": "mazar_travel"}, {"sql": "select `core_news`.* from `core_news` where `core_news`.`status` = 'publish' and `core_news`.`deleted_at` is null group by `core_news`.`id` order by `core_news`.`id` asc limit 6", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 134}, {"index": 16, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 89}, {"index": 19, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 22, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 23, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.922805, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "ListNews.php:134", "source": "modules/News/Blocks/ListNews.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FBlocks%2FListNews.php&line=134", "ajax": false, "filename": "ListNews.php", "line": "134"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_translations` where `locale` = 'en' and `core_news_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 134}, {"index": 21, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 89}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.943444, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ListNews.php:134", "source": "modules/News/Blocks/ListNews.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FBlocks%2FListNews.php&line=134", "ajax": false, "filename": "ListNews.php", "line": "134"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category` where `core_news_category`.`id` in (1, 2, 3) and `core_news_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 134}, {"index": 21, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 89}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1751924568.961185, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ListNews.php:134", "source": "modules/News/Blocks/ListNews.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FBlocks%2FListNews.php&line=134", "ajax": false, "filename": "ListNews.php", "line": "134"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category_translations` where `core_news_category_translations`.`origin_id` = 3 and `core_news_category_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["3", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "News::frontend.blocks.list-news.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1751924568.9928122, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category_translations` where `core_news_category_translations`.`origin_id` = 2 and `core_news_category_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["2", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "News::frontend.blocks.list-news.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1751924569.03302, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category_translations` where `core_news_category_translations`.`origin_id` = 1 and `core_news_category_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "News::frontend.blocks.list-news.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1751924569.13307, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1751924569.75771, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1751924569.775506, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1751924569.879797, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1751924570.1063051, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Location\\Models\\Location": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "Modules\\Core\\Models\\Terms": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FTerms.php&line=1", "ajax": false, "filename": "Terms.php", "line": "?"}}, "Modules\\Car\\Models\\Car": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=1", "ajax": false, "filename": "Car.php", "line": "?"}}, "Modules\\Tour\\Models\\Tour": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=1", "ajax": false, "filename": "Tour.php", "line": "?"}}, "Modules\\News\\Models\\News": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModels%2FNews.php&line=1", "ajax": false, "filename": "News.php", "line": "?"}}, "Modules\\Flight\\Models\\SeatType": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FFlight%2FModels%2FSeatType.php&line=1", "ajax": false, "filename": "SeatType.php", "line": "?"}}, "Modules\\Tour\\Models\\TourCategory": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTourCategory.php&line=1", "ajax": false, "filename": "TourCategory.php", "line": "?"}}, "Modules\\Hotel\\Models\\Hotel": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=1", "ajax": false, "filename": "Hotel.php", "line": "?"}}, "Modules\\Space\\Models\\Space": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=1", "ajax": false, "filename": "Space.php", "line": "?"}}, "Modules\\Event\\Models\\Event": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "Modules\\Boat\\Models\\Boat": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBoat%2FModels%2FBoat.php&line=1", "ajax": false, "filename": "Boat.php", "line": "?"}}, "Modules\\News\\Models\\NewsCategory": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModels%2FNewsCategory.php&line=1", "ajax": false, "filename": "NewsCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Modules\\Page\\Models\\Page": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FPage%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Modules\\Template\\Models\\Template": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FModels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}, "Modules\\Template\\Models\\TemplateTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FModels%2FTemplateTranslation.php&line=1", "ajax": false, "filename": "TemplateTranslation.php", "line": "?"}}}, "count": 111, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "bc_current_currency": "eur"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-684537815 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-684537815\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-291944731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-291944731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2036132472 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2036132472\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/mazar/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IkFmLzBxbUgvbjdDRXh1NlZBLy92T2c9PSIsInZhbHVlIjoiYUs3YTJGSWxHVGZzTkNrNFNxTU9tbkpvVG1HTmVxSjVKUng4QXZYRzlOL3c5R1JsKzZ4QmtaSU50djlPMkcwNzBoZmJVeUduWGFXUXBodWhZZ2l0aldPckUvaHVIMXowb2tCMkRUL3pqMDEycEFkb0gzR29MNzMxdkhMNjhYZkkiLCJtYWMiOiI5Njg1YzM1ODI3MzZjNDQ3ZTMwYWViZWI3OGI2YTU3YmU5M2UyYzdmYzdmMTU3ZTlmZTkxZGU2MzNhZGE2NmQ0IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6InloN3JaZzA0NEJJTnFtSE1oZVdGd1E9PSIsInZhbHVlIjoieDZ5SFYxcElxcjdBZkNpaGlUVzkrWDRmNjlKalViZUlYcG5GSjg5VFRNZVdRSXphN2dBQUZvZGZIaTc2K042aWxJWTFJb0p3N2twSG1sUUltRWhlN1VzazQrVk9hbTVPaE1GOXkrOS9OQTh3SjN0QUFScUhZUGRZMjRoNktSUU4iLCJtYWMiOiJiMThmZWJmY2QxZmU0MWU4NjViMTYzOWNiNzA4YWU4NzQ1ODE4ZGU2ZGZiYjllYjcyYmZhZmFjODZiZDBkZTQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2080010834 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080010834\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1031955976 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:42:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxzUGdTa0xqQjdITjJVNzAwRSszRlE9PSIsInZhbHVlIjoiUDVsSmlENWRNWU5UMk9CVHk4QjRGWUpPc1M0QVhZMHBUWEVMMUZ0RmV4ZDJiWUFiUkxvZG9OUnNTTUFQOGZhYW1yWUlQWC9nb2tFcVlJYzRZSDA1dWZmODg1STc5VE9FUmQ1cWNSNHd5ZjBJQTltMHVjVFFqcHR5TWw3RUpjZDUiLCJtYWMiOiJjNjlkYjk3NjNmN2YyZmMwOTZhNjgwNjNjMmQwYTgwNzc3YTMxNzc5NTdjZmI5ZDNkZTM3YjE2NTA2ZWE4Yjg0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:42:50 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IitLN0twR2ZxQWM0Slhzd3JSQnpjTGc9PSIsInZhbHVlIjoiYTRuVFNHL1F3TXgrQWFiODRrYUt6dk9HZWI2M01qN1dDU1hiUUw2Q2t5UzJjWmdlUGR5T3dkMFBLTzBZWXRSVGh0L3pzK0czMVNISjB5QWovK2FuK0tkYi8xMTBQQnpvUXAvTGNKVCtuMi92NnFBNnc4Q2JmakUzVHFQdkM0a0wiLCJtYWMiOiI4ZDYzZTQ3OTMyZDAyMWQ5Y2FhMjY1NzUyZDNiZGNkMzBhNzM0MDM0YjdkY2M3ODdhZWU1NzM4MDMyOThmOWFmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:42:50 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxzUGdTa0xqQjdITjJVNzAwRSszRlE9PSIsInZhbHVlIjoiUDVsSmlENWRNWU5UMk9CVHk4QjRGWUpPc1M0QVhZMHBUWEVMMUZ0RmV4ZDJiWUFiUkxvZG9OUnNTTUFQOGZhYW1yWUlQWC9nb2tFcVlJYzRZSDA1dWZmODg1STc5VE9FUmQ1cWNSNHd5ZjBJQTltMHVjVFFqcHR5TWw3RUpjZDUiLCJtYWMiOiJjNjlkYjk3NjNmN2YyZmMwOTZhNjgwNjNjMmQwYTgwNzc3YTMxNzc5NTdjZmI5ZDNkZTM3YjE2NTA2ZWE4Yjg0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:42:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IitLN0twR2ZxQWM0Slhzd3JSQnpjTGc9PSIsInZhbHVlIjoiYTRuVFNHL1F3TXgrQWFiODRrYUt6dk9HZWI2M01qN1dDU1hiUUw2Q2t5UzJjWmdlUGR5T3dkMFBLTzBZWXRSVGh0L3pzK0czMVNISjB5QWovK2FuK0tkYi8xMTBQQnpvUXAvTGNKVCtuMi92NnFBNnc4Q2JmakUzVHFQdkM0a0wiLCJtYWMiOiI4ZDYzZTQ3OTMyZDAyMWQ5Y2FhMjY1NzUyZDNiZGNkMzBhNzM0MDM0YjdkY2M3ODdhZWU1NzM4MDMyOThmOWFmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:42:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031955976\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost/mazar/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}