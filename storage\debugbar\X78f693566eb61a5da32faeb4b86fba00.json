{"__meta": {"id": "X78f693566eb61a5da32faeb4b86fba00", "datetime": "2025-07-07 21:42:35", "utime": **********.645717, "method": "GET", "uri": "/mazar/public/custom-css", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.163821, "end": **********.645742, "duration": 0.4819209575653076, "duration_str": "482ms", "measures": [{"label": "Booting", "start": **********.163821, "relative_start": 0, "end": **********.571446, "relative_end": **********.571446, "duration": 0.4076249599456787, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.571478, "relative_start": 0.4076569080352783, "end": **********.645746, "relative_end": 4.0531158447265625e-06, "duration": 0.07426810264587402, "duration_str": "74.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5082440, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FControllers%2FStyleController.php&line=9\" onclick=\"\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.03074, "accumulated_duration_str": "30.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5982912, "duration": 0.03015, "duration_str": "30.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.634449, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "bc_current_currency": "usd", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-1063032654 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1063032654\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-627399749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-627399749\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2015341227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2015341227\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1699253325 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/mazar/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IkpjbjZBQnZGOTRIbEZJZ2RlNkJ5eUE9PSIsInZhbHVlIjoiaTVMWVF3VXk4VUJqWkxvZVc1YmpidENxWWg5NUEyYTdqNVdxL0NnT1dHWlQ2MTkwN3E3KzFkZThLZkJlWGpLcHdRd1RyMENPSXZDUE9TdFBSZDhyTmFjeVF0L1dmcUhZNnoxQ2R4dm1JNmRSOXhRWityZEtZUU1yQW82SGJzTFYiLCJtYWMiOiJjYmY1YTQwMzAyNTFmZmY5MzRlZTUwOTljMjg5MjNiY2Q5ZDczMDdlZWJhYWQ5NGQ0YmNkOGNjNGI1MjdlODZjIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IjYrM1g0OHFJbng4b1U0NmNjcDM4bVE9PSIsInZhbHVlIjoiWFRxdlFIQlg1aHhZd2RJbFN3UVRNalkvTTVENjRHQjBVaUdHQmQ0OFp3bVhqa2pad1F0TC9sN05yMkJtSit3VmpkWUZzbmJ4YklHdXZSeVZ3WnNDclZVVTVNSUo5L0RDQWZGcDBvbXU5WFdqVXAydmtvRDBvdk9uZTlrMmI3a0kiLCJtYWMiOiI1ZWU5ZjVkNmYyYjc2ZDc4ZTBkYmQyYmY2ODYyNzc5MTgwN2I1YTU4NDJhZjQ2OTNkOTQ1ZDFmYmU3OGZkNTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699253325\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1860050091 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860050091\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1558989233 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:42:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImszR044dFE1Nm94NFhRT09MOTRjR1E9PSIsInZhbHVlIjoiaXhVRy8zVTZRb2xVM1Y2cWpBMDhia2tBdEtDamFOZU81ZGpxVVRVTU1mQjJORG9QanZ3U00veUVjRHVwdUxHUlkyMUZtWU1EaXF2QXM1NktXSnQxNVh3blV0VjdWVHJhRUVoeFBkbjErY2J6Z2J4YnhmK2xPMk51WWlhY3RmL2siLCJtYWMiOiIwNDY2MmE1NWFjYzIzYzg1MWUwZWNmMDk2ZWQzYjI5Y2ZmODJkZjM0MmZlNDg5Y2RiMGQ1MDY2YjgxNWI4ODUyIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:42:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjQvZWdXaStnSTlXQXNZR3NaT3Bvc2c9PSIsInZhbHVlIjoidU9ReWlOcjh1ck03dDBjRFliRERkZkJrTTZxNkFEMjF6YzRDQTQ1REdlSGk3NjU5TGNka1BCc2pEaXdPQjd3WnR5Wk1BQ1VxZnRzOENGQ3lGM1l3MWFmMlc5dENaRXJONnVEU2JhNW5mOWFRSEZBVXAxU2I3UGdTZEtRdWlGai8iLCJtYWMiOiJlZWNlZGU3M2RkZTIwNGYyZjg0NzYyYzExN2RmYWQxYzlhYTJhNWUyZjE0YTYzMjgyMGM5M2E1NTkzMmQ5YjJmIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:42:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImszR044dFE1Nm94NFhRT09MOTRjR1E9PSIsInZhbHVlIjoiaXhVRy8zVTZRb2xVM1Y2cWpBMDhia2tBdEtDamFOZU81ZGpxVVRVTU1mQjJORG9QanZ3U00veUVjRHVwdUxHUlkyMUZtWU1EaXF2QXM1NktXSnQxNVh3blV0VjdWVHJhRUVoeFBkbjErY2J6Z2J4YnhmK2xPMk51WWlhY3RmL2siLCJtYWMiOiIwNDY2MmE1NWFjYzIzYzg1MWUwZWNmMDk2ZWQzYjI5Y2ZmODJkZjM0MmZlNDg5Y2RiMGQ1MDY2YjgxNWI4ODUyIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:42:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjQvZWdXaStnSTlXQXNZR3NaT3Bvc2c9PSIsInZhbHVlIjoidU9ReWlOcjh1ck03dDBjRFliRERkZkJrTTZxNkFEMjF6YzRDQTQ1REdlSGk3NjU5TGNka1BCc2pEaXdPQjd3WnR5Wk1BQ1VxZnRzOENGQ3lGM1l3MWFmMlc5dENaRXJONnVEU2JhNW5mOWFRSEZBVXAxU2I3UGdTZEtRdWlGai8iLCJtYWMiOiJlZWNlZGU3M2RkZTIwNGYyZjg0NzYyYzExN2RmYWQxYzlhYTJhNWUyZjE0YTYzMjgyMGM5M2E1NTkzMmQ5YjJmIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:42:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558989233\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/mazar/public/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}