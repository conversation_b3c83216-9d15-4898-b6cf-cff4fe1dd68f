{"__meta": {"id": "X9fcbb1b0be31ed452e2a94b08ee0e330", "datetime": "2025-07-07 21:34:11", "utime": **********.538548, "method": "POST", "uri": "/mazar/public/login", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751924048.800824, "end": **********.538588, "duration": 2.7377641201019287, "duration_str": "2.74s", "measures": [{"label": "Booting", "start": 1751924048.800824, "relative_start": 0, "end": 1751924049.427794, "relative_end": 1751924049.427794, "duration": 0.6269700527191162, "duration_str": "627ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751924049.427813, "relative_start": 0.6269891262054443, "end": **********.53859, "relative_end": 1.9073486328125e-06, "duration": 2.110776901245117, "duration_str": "2.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5754360, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "domain": null, "middleware": "web, guest:web, throttle:login", "controller": "Laravel\\Fortify\\Http\\Controllers\\AuthenticatedSessionController@store", "namespace": "Laravel\\Fortify\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Ffortify%2Fsrc%2FHttp%2FControllers%2FAuthenticatedSessionController.php&line=58\" onclick=\"\">vendor/laravel/fortify/src/Http/Controllers/AuthenticatedSessionController.php:58-63</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00604, "accumulated_duration_str": "6.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/FortifyServiceProvider.php", "file": "C:\\wamp64\\www\\mazar\\app\\Providers\\FortifyServiceProvider.php", "line": 68}, {"index": 19, "namespace": null, "name": "vendor/laravel/fortify/src/Actions/AttemptToAuthenticate.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\fortify\\src\\Actions\\AttemptToAuthenticate.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}, {"index": 22, "namespace": null, "name": "vendor/laravel/fortify/src/Http/Controllers/AuthenticatedSessionController.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\fortify\\src\\Http\\Controllers\\AuthenticatedSessionController.php", "line": 60}], "start": **********.0216868, "duration": 0.00604, "duration_str": "6.04ms", "memory": 0, "memory_str": null, "filename": "FortifyServiceProvider.php:68", "source": "app/Providers/FortifyServiceProvider.php:68", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FProviders%2FFortifyServiceProvider.php&line=68", "ajax": false, "filename": "FortifyServiceProvider.php", "line": "68"}, "connection": "mazar_travel"}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:43 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-888035729 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-888035729\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1875455565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1875455565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1032385624 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>remember</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>redirect</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032385624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1141471040 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">78</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fvaLd7mqR2GdTsywFJ4CUvyBeElAH21zDSguYalh</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/mazar/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IllBWW9wVVBRYnc5VUY4WlUwV1d2SkE9PSIsInZhbHVlIjoiSHM4bUYrdGxRWHkrWWk4T2FRN1JEcEViT0lVWjdPdjhIcXVEMXNYMUt5OVJhQjJaYkJCdmVTZDNQbmtDaEdLSnViT1Z0aEFmdFVhcm5sVXNqN3VkVEJ5Tnd4U3RwUHdROU41OXZ0TklkbWJCSDV2VElsZHcxSjQ5RWlFRUQzMHgiLCJtYWMiOiJiYTU4M2NmMTU2OGI1MmIwZWQ4NTZiY2JjMDIwYWI2YzljNzlhMjYzZGU0YTA4N2Q5NDA1NTA0NTRmZTM4ZDdiIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IjRPM2VocnJPSFhhaTRJdEt5TnhFcUE9PSIsInZhbHVlIjoibVZkUzZaQVhLMy9HTmhlNUNlektSNzV3RmZZRjRMM3g4a3R5dTh2Mkp3RVlWaXNSVXFjdWdlRXE0TFBTNGM1dEp0Uk5ramYySkwzUXpCYy9xQ09iNmEvRDJENDc4UjZ6S0IvdGs1TEs4aHhGRzVuT21hYXA2ZUpYMWUxZHZMVzUiLCJtYWMiOiJhNDY3NTdiZTg1Nzk1NmY2NDMzYTIwNWY4ZjU5OGY1MDY3NWQ1NDdjMjU5MDlmNWM2YmVjNDNmZGU0NWY0MGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141471040\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fvaLd7mqR2GdTsywFJ4CUvyBeElAH21zDSguYalh</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wskoxbfRzLXVdH319d5WARiJUV5yxWxb6WNHoPGX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:34:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ilg0K2lVc3JmeEJEa2ExVVR0VkxNcGc9PSIsInZhbHVlIjoib0hkMWhPbTlZdmU3YkVVRnp1K2N4emxndjhmaElkNk1ybi9xbWI1SWROQU9PNEZrVWd1Z2dBQkxzZ2lmN1h1bXhLZTV1L016VG16MlQ1NDhYdXRibEtkUkQzVnhETU5XQ04vUDVRTldNeDVmNHNUUmRCcmlpKytMUzB4bkMvcFoiLCJtYWMiOiI5ZGE3NmYxNTJhODIxNDVlOWI0YzY1ZTE2YzBmMjQzYzc3OTQ2NmM4YzkyZDBhYmI3YWQ5OTZmMjM0NzA4YzA1IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:34:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6ImdaSWdBZGliZVZqWDRjQ2ZwY2VFTmc9PSIsInZhbHVlIjoiTmZaV01TNXNISkFwdmNrNzNWMHNzNmFnalZCZVdEQXRUSHR0TkI1enFHbit3N3RMemNBV01neTMzOEtzU3RrNDFLZW9rWWxSeklDZmpiQ21hWThSMDQyUitjam9Oa2RvelE1TEZLc2ZDZnQ3NjV6UDJtQTdlbXFkdUh2MVRJb2wiLCJtYWMiOiI4Mjc5Y2YwYTZjMGE4ODc5ZjEyNTJmZWNkNGNlNWFkYTY5OGI1OTYxZmNiZDUwMWFjOTBkNDFiMWM1Y2FmZmI2IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:34:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ilg0K2lVc3JmeEJEa2ExVVR0VkxNcGc9PSIsInZhbHVlIjoib0hkMWhPbTlZdmU3YkVVRnp1K2N4emxndjhmaElkNk1ybi9xbWI1SWROQU9PNEZrVWd1Z2dBQkxzZ2lmN1h1bXhLZTV1L016VG16MlQ1NDhYdXRibEtkUkQzVnhETU5XQ04vUDVRTldNeDVmNHNUUmRCcmlpKytMUzB4bkMvcFoiLCJtYWMiOiI5ZGE3NmYxNTJhODIxNDVlOWI0YzY1ZTE2YzBmMjQzYzc3OTQ2NmM4YzkyZDBhYmI3YWQ5OTZmMjM0NzA4YzA1IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:34:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6ImdaSWdBZGliZVZqWDRjQ2ZwY2VFTmc9PSIsInZhbHVlIjoiTmZaV01TNXNISkFwdmNrNzNWMHNzNmFnalZCZVdEQXRUSHR0TkI1enFHbit3N3RMemNBV01neTMzOEtzU3RrNDFLZW9rWWxSeklDZmpiQ21hWThSMDQyUitjam9Oa2RvelE1TEZLc2ZDZnQ3NjV6UDJtQTdlbXFkdUh2MVRJb2wiLCJtYWMiOiI4Mjc5Y2YwYTZjMGE4ODc5ZjEyNTJmZWNkNGNlNWFkYTY5OGI1OTYxZmNiZDUwMWFjOTBkNDFiMWM1Y2FmZmI2IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:34:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1198602186 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/mazar/public/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198602186\", {\"maxDepth\":0})</script>\n"}}