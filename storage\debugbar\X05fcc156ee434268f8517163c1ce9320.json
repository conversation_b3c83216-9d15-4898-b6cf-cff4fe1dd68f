{"__meta": {"id": "X05fcc156ee434268f8517163c1ce9320", "datetime": "2025-07-07 21:34:32", "utime": 1751924072.254825, "method": "GET", "uri": "/mazar/public/admin", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751924068.969769, "end": 1751924072.254854, "duration": 3.2850849628448486, "duration_str": "3.29s", "measures": [{"label": "Booting", "start": 1751924068.969769, "relative_start": 0, "end": **********.404838, "relative_end": **********.404838, "duration": 0.43506908416748047, "duration_str": "435ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.404855, "relative_start": 0.4350860118865967, "end": 1751924072.254857, "relative_end": 3.0994415283203125e-06, "duration": 2.8500020503997803, "duration_str": "2.85s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 9258512, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "1x Dashboard::index", "param_count": null, "params": [], "start": **********.785878, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Dashboard/Views/index.blade.phpDashboard::index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FDashboard%2FViews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Dashboard::index"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.954613, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x Layout::admin.app", "param_count": null, "params": [], "start": **********.984787, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/app.blade.phpLayout::admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.app"}, {"name": "1x Layout::admin.parts.global-script", "param_count": null, "params": [], "start": 1751924070.178496, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/global-script.blade.phpLayout::admin.parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.global-script"}, {"name": "1x Layout::admin.parts.header", "param_count": null, "params": [], "start": 1751924070.252678, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.phpLayout::admin.parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.header"}, {"name": "1x Layout::admin.parts.sidebar", "param_count": null, "params": [], "start": 1751924070.496303, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.phpLayout::admin.parts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.sidebar"}, {"name": "1x Layout::admin.parts.bc", "param_count": null, "params": [], "start": 1751924072.032318, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/bc.blade.phpLayout::admin.parts.bc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fbc.blade.php&line=1", "ajax": false, "filename": "bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.bc"}, {"name": "1x Media::browser", "param_count": null, "params": [], "start": 1751924072.08157, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Media/Views/browser.blade.phpMedia::browser", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FViews%2Fbrowser.blade.php&line=1", "ajax": false, "filename": "browser.blade.php", "line": "?"}, "render_count": 1, "name_original": "Media::browser"}, {"name": "1x Pro::admin.upgrade-modal", "param_count": null, "params": [], "start": 1751924072.160324, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\app\\Pro/Views/admin/upgrade-modal.blade.phpPro::admin.upgrade-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FPro%2FViews%2Fadmin%2Fupgrade-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Pro::admin.upgrade-modal"}, {"name": "1x Ai::frontend.text-generate", "param_count": null, "params": [], "start": 1751924072.187587, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\pro\\Ai/Views/frontend/text-generate.blade.phpAi::frontend.text-generate", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fpro%2FAi%2FViews%2Ffrontend%2Ftext-generate.blade.php&line=1", "ajax": false, "filename": "text-generate.blade.php", "line": "?"}, "render_count": 1, "name_original": "Ai::frontend.text-generate"}]}, "route": {"uri": "GET admin", "middleware": "web, dashboard", "controller": "Modules\\Dashboard\\Admin\\DashboardController@index", "namespace": "Modules\\Dashboard\\Admin", "prefix": "admin", "where": [], "as": "admin.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FDashboard%2FAdmin%2FDashboardController.php&line=11\" onclick=\"\">modules/Dashboard/Admin/DashboardController.php:11-20</a>"}, "queries": {"nb_statements": 46, "nb_failed_statements": 0, "accumulated_duration": 0.05861, "accumulated_duration_str": "58.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.493921, "duration": 0.0299, "duration_str": "29.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5309482, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_bookings` where `status` != 'draft' and `bravo_bookings`.`deleted_at` is null order by `id` desc limit 10", "type": "query", "params": [], "bindings": ["draft"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>!=</code> operator is not standard. Use the <code>&lt;&gt;</code> operator to test for inequality instead."], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 298}, {"index": 16, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.586254, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Booking.php:298", "source": "modules/Booking/Models/Booking.php:298", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=298", "ajax": false, "filename": "Booking.php", "line": "298"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price , sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning  from `bravo_bookings` where `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 305}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.591765, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Booking.php:305", "source": "modules/Booking/Models/Booking.php:305", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=305", "ajax": false, "filename": "Booking.php", "line": "305"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_bookings` where `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["draft", "cancelled", "unpaid"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 306}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.59724, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Booking.php:306", "source": "modules/Booking/Models/Booking.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=306", "ajax": false, "filename": "Booking.php", "line": "306"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_hotels` where `status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.608047, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_spaces` where `status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6134791, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_cars` where `status` = 'publish' and `bravo_cars`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.617966, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_events` where `status` = 'publish' and `bravo_events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.622074, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_tours` where `status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.626239, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_flight` where `status` = 'publish' and `bravo_flight`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.631547, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_boats` where `status` = 'publish' and `bravo_boats`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 312}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.636055, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Booking.php:312", "source": "modules/Booking/Models/Booking.php:312", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=312", "ajax": false, "filename": "Booking.php", "line": "312"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 00:00:00' and '2025-07-07 00:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 00:00:00", "2025-07-07 00:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.674903, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 01:00:00' and '2025-07-07 01:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 01:00:00", "2025-07-07 01:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.680166, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 02:00:00' and '2025-07-07 02:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 02:00:00", "2025-07-07 02:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6843572, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 03:00:00' and '2025-07-07 03:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 03:00:00", "2025-07-07 03:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6885731, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 04:00:00' and '2025-07-07 04:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 04:00:00", "2025-07-07 04:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.693045, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 05:00:00' and '2025-07-07 05:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 05:00:00", "2025-07-07 05:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.69859, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 06:00:00' and '2025-07-07 06:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 06:00:00", "2025-07-07 06:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.70292, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 07:00:00' and '2025-07-07 07:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 07:00:00", "2025-07-07 07:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.707353, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 08:00:00' and '2025-07-07 08:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 08:00:00", "2025-07-07 08:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.712163, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 09:00:00' and '2025-07-07 09:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 09:00:00", "2025-07-07 09:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.717059, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 10:00:00' and '2025-07-07 10:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 10:00:00", "2025-07-07 10:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.721338, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 11:00:00' and '2025-07-07 11:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 11:00:00", "2025-07-07 11:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.725676, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 12:00:00' and '2025-07-07 12:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 12:00:00", "2025-07-07 12:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.731048, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 13:00:00' and '2025-07-07 13:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 13:00:00", "2025-07-07 13:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.735461, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 14:00:00' and '2025-07-07 14:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 14:00:00", "2025-07-07 14:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7396529, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 15:00:00' and '2025-07-07 15:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 15:00:00", "2025-07-07 15:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.744305, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 16:00:00' and '2025-07-07 16:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 16:00:00", "2025-07-07 16:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7486858, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 17:00:00' and '2025-07-07 17:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 17:00:00", "2025-07-07 17:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7528079, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 18:00:00' and '2025-07-07 18:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 18:00:00", "2025-07-07 18:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.757185, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 19:00:00' and '2025-07-07 19:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 19:00:00", "2025-07-07 19:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.762493, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 20:00:00' and '2025-07-07 20:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 20:00:00", "2025-07-07 20:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.767238, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 21:00:00' and '2025-07-07 21:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 21:00:00", "2025-07-07 21:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.771514, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 22:00:00' and '2025-07-07 22:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 22:00:00", "2025-07-07 22:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.775582, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select sum(`total`) as total_price,sum( `total` - `total_before_fees` + `commission` - `vendor_service_fee_amount` ) AS total_earning from `bravo_bookings` where `created_at` between '2025-07-07 23:00:00' and '2025-07-07 23:59:59' and `status` not in ('draft', 'cancelled', 'unpaid') and `bravo_bookings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2025-07-07 23:00:00", "2025-07-07 23:59:59", "draft", "cancelled", "unpaid"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Booking.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Booking.php", "line": 411}, {"index": 17, "namespace": null, "name": "modules/Dashboard/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Dashboard\\Admin\\DashboardController.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.780257, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Booking.php:411", "source": "modules/Booking/Models/Booking.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=411", "ajax": false, "filename": "Booking.php", "line": "411"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1751924070.428044, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1751924070.432863, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924070.4862409, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 132}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1751924070.4901161, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_news` where `status` = 'pending' and `core_news`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/News/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\ModuleProvider.php", "line": 31}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.41976, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:31", "source": "modules/News/ModuleProvider.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModuleProvider.php&line=31", "ajax": false, "filename": "ModuleProvider.php", "line": "31"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `users` where `verify_submit_status` in ('new', 'partial') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["new", "partial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 407}, {"index": 17, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 47}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.426602, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "User.php:407", "source": "app/User.php:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=407", "ajax": false, "filename": "User.php", "line": "407"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `user_upgrade_request` where `status` = 'pending' and exists (select * from `users` where `user_upgrade_request`.`user_id` = `users`.`id` and `users`.`deleted_at` is null) and `user_upgrade_request`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 48}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.436794, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:48", "source": "modules/User/ModuleProvider.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=48", "ajax": false, "filename": "ModuleProvider.php", "line": "48"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_booking_payments` where `object_model` = 'plan' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["plan", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 93}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.502369, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:93", "source": "modules/User/ModuleProvider.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=93", "ajax": false, "filename": "ModuleProvider.php", "line": "93"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_booking_payments` where `object_model` = 'wallet_deposit' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["wallet_deposit", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Wallet/DepositPayment.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Wallet\\DepositPayment.php", "line": 15}, {"index": 17, "namespace": null, "name": "modules/Report/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Report\\ModuleProvider.php", "line": 18}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.51215, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "DepositPayment.php:15", "source": "modules/User/Models/Wallet/DepositPayment.php:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FWallet%2FDepositPayment.php&line=15", "ajax": false, "filename": "DepositPayment.php", "line": "15"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_payouts` where `status` = 'initial'", "type": "query", "params": [], "bindings": ["initial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Vendor/Models/VendorPayout.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\Models\\VendorPayout.php", "line": 63}, {"index": 17, "namespace": null, "name": "modules/Vendor/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\ModuleProvider.php", "line": 27}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.5397549, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "VendorPayout.php:63", "source": "modules/Vendor/Models/VendorPayout.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FModels%2FVendorPayout.php&line=63", "ajax": false, "filename": "VendorPayout.php", "line": "63"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Booking\\Models\\Booking": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBooking.php&line=1", "ajax": false, "filename": "Booking.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-466420688 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-466420688\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1503839549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1503839549\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-623943930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-623943930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1130910723 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/mazar/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6Im1oNW4vVXUyQjlLM1hJOHhSOTRTTFE9PSIsInZhbHVlIjoibDVvRnUwa0Q0Qlp2T0hFRUdHSUx6c1BPZFVnR0h5NlZteUVYV3p0bDU5dkF4QTVlSjlKdzduY25sOXdrWTNad042OTBxS092TWFNd2pEeldOMEZaaXBja1c4dC9FOUJicnVqUkhwRmQvS1J6ZVBoSFIyUzg0T0JGSkt0endROEEiLCJtYWMiOiJiMmY3YTQyMTFkMDU0YWY3MTgwNmM5ZGE3ZjM5Y2VkNmFjMGZkNjA4NWE0ODY4NWRhYTg5Mzc0M2EzMTVmNWZlIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IkxINU1hRVVpVkR6NlJyQXFnSmpJVHc9PSIsInZhbHVlIjoiY3BVV0djQUR1Zm51VENTdmdmQlJldHlYRU1FaDh5RlB2Wk8xQmJ0UFNjY3Rka2RLYTFsQjcvRWV6R3NXTWNZV1FXaDBEOVpicEg4SGx1bUR2dnZOeXJaUVR6Z2dJNm1RSWRuNFRzQklpa1paekdiSThUTTFGOWswZ1B2SW1DUUwiLCJtYWMiOiJiYzViMDUxMGEyMWMxOWM1MDU1OTdhYTU0ZGExNTNiYjU4NzhjYTcyNGY1ZmMyZDMyZjhmMGFmOWViMDdiZDRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130910723\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1854386031 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854386031\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1850804908 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:34:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imt4Y2hVTUNYblB2Y3JXalZlenZid3c9PSIsInZhbHVlIjoiMndpY0grM0dwV2UwSUFPYTloRTJMclYyR0lmckxUWXRjY0pLU0M0aXJ6VXBZK01Yb3ZwYzdEdUlBbnRrMGp1ZFdiellDRzcrcldXMEgxaGw4SnlIam9Wd2prVHNQNFRUYlMrUkZzWXZOMGh0MjJVR2g2MndRVlMreVB2RHk4bHYiLCJtYWMiOiJlNzI2ODc2ODJmMTc1ZjBiNGUxZjYwNWU0OWEyYWI4YzRlNTJlYzJkNjM4OGFmNmU3OTZkOTFkNzIzNzg2ZGM0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:34:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjhLTktkS2FUT2RJWnI5eHVHZlR4N0E9PSIsInZhbHVlIjoiUDJwOXhlcmhGMnBZWXdrMXBKdVJaWWNqMFMydkZmcGRmVjZjWkZOYVdpNCtoK1dIZmZwV0R1dnpRTGx3OVIzVXVuQ282UXRud1VrMlBHRWlKUWcvc2NDMS9ZQVZhQi9TWEdjSTJyMms5ZnhvYjVqVXNDc0lGZEpZNCswYWs3cHAiLCJtYWMiOiIzYzE2MGEwMmFiNGQ5MTIzNzdlMmMxM2VkNWNhNDQ3N2FkZWQ3MDliYmRmOTVhNjJmZjMzN2ViYTI5YzcyZDcxIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:34:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imt4Y2hVTUNYblB2Y3JXalZlenZid3c9PSIsInZhbHVlIjoiMndpY0grM0dwV2UwSUFPYTloRTJMclYyR0lmckxUWXRjY0pLU0M0aXJ6VXBZK01Yb3ZwYzdEdUlBbnRrMGp1ZFdiellDRzcrcldXMEgxaGw4SnlIam9Wd2prVHNQNFRUYlMrUkZzWXZOMGh0MjJVR2g2MndRVlMreVB2RHk4bHYiLCJtYWMiOiJlNzI2ODc2ODJmMTc1ZjBiNGUxZjYwNWU0OWEyYWI4YzRlNTJlYzJkNjM4OGFmNmU3OTZkOTFkNzIzNzg2ZGM0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:34:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjhLTktkS2FUT2RJWnI5eHVHZlR4N0E9PSIsInZhbHVlIjoiUDJwOXhlcmhGMnBZWXdrMXBKdVJaWWNqMFMydkZmcGRmVjZjWkZOYVdpNCtoK1dIZmZwV0R1dnpRTGx3OVIzVXVuQ282UXRud1VrMlBHRWlKUWcvc2NDMS9ZQVZhQi9TWEdjSTJyMms5ZnhvYjVqVXNDc0lGZEpZNCswYWs3cHAiLCJtYWMiOiIzYzE2MGEwMmFiNGQ5MTIzNzdlMmMxM2VkNWNhNDQ3N2FkZWQ3MDliYmRmOTVhNjJmZjMzN2ViYTI5YzcyZDcxIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:34:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850804908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1024212027 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/mazar/public/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024212027\", {\"maxDepth\":0})</script>\n"}}