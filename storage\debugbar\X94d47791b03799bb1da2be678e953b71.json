{"__meta": {"id": "X94d47791b03799bb1da2be678e953b71", "datetime": "2025-07-07 21:36:05", "utime": **********.124206, "method": "POST", "uri": "/mazar/public/admin/module/core/settings/store/general", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.394761, "end": **********.124235, "duration": 0.7294738292694092, "duration_str": "729ms", "measures": [{"label": "Booting", "start": **********.394761, "relative_start": 0, "end": **********.800613, "relative_end": **********.800613, "duration": 0.4058518409729004, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.800655, "relative_start": 0.40589380264282227, "end": **********.124238, "relative_end": 3.0994415283203125e-06, "duration": 0.32358312606811523, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5320912, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/module/core/settings/store/{group}", "middleware": "web, dashboard", "controller": "Modules\\Core\\Admin\\SettingsController@store", "namespace": "Modules\\Core\\Admin", "prefix": "admin/module/core", "where": [], "as": "core.admin.settings.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FAdmin%2FSettingsController.php&line=60\" onclick=\"\">modules/Core/Admin/SettingsController.php:60-124</a>"}, "queries": {"nb_statements": 23, "nb_failed_statements": 0, "accumulated_duration": 0.050330000000000014, "accumulated_duration_str": "50.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8252501, "duration": 0.02994, "duration_str": "29.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.859814, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_title' limit 1", "type": "query", "params": [], "bindings": ["site_title"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.894563, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_desc' limit 1", "type": "query", "params": [], "bindings": ["site_desc"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9203022, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_favicon' limit 1", "type": "query", "params": [], "bindings": ["site_favicon"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.927837, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'home_page_id' limit 1", "type": "query", "params": [], "bindings": ["home_page_id"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.934573, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'topbar_left_text' limit 1", "type": "query", "params": [], "bindings": ["topbar_left_text"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9412901, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'logo_id' limit 1", "type": "query", "params": [], "bindings": ["logo_id"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.948426, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'footer_text_left' limit 1", "type": "query", "params": [], "bindings": ["footer_text_left"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.955924, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'footer_text_right' limit 1", "type": "query", "params": [], "bindings": ["footer_text_right"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.963632, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'list_widget_footer' limit 1", "type": "query", "params": [], "bindings": ["list_widget_footer"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9702759, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'date_format' limit 1", "type": "query", "params": [], "bindings": ["date_format"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.977075, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_timezone' limit 1", "type": "query", "params": [], "bindings": ["site_timezone"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.984242, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_locale' limit 1", "type": "query", "params": [], "bindings": ["site_locale"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9916189, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_first_day_of_the_weekin_calendar' limit 1", "type": "query", "params": [], "bindings": ["site_first_day_of_the_weekin_calendar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.998336, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_enable_multi_lang' limit 1", "type": "query", "params": [], "bindings": ["site_enable_multi_lang"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.005733, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "update `core_settings` set `val` = '1', `core_settings`.`updated_at` = '2025-07-07 21:36:05' where `id` = 2", "type": "query", "params": [], "bindings": ["1", "2025-07-07 21:36:05", "2"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 66}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.012672, "duration": 0.00524, "duration_str": "5.24ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'enable_rtl' limit 1", "type": "query", "params": [], "bindings": ["enable_rtl"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0243611, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'page_contact_title' limit 1", "type": "query", "params": [], "bindings": ["page_contact_title"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.031251, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'page_contact_sub_title' limit 1", "type": "query", "params": [], "bindings": ["page_contact_sub_title"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.038125, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'page_contact_desc' limit 1", "type": "query", "params": [], "bindings": ["page_contact_desc"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.04523, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'page_contact_image' limit 1", "type": "query", "params": [], "bindings": ["page_contact_image"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.052145, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'home_hotel_id' limit 1", "type": "query", "params": [], "bindings": ["home_hotel_id"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.060164, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:57", "source": "app/Helpers/AppHelper.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=57", "ajax": false, "filename": "AppHelper.php", "line": "57"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Core\\Models\\Settings": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 22, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/admin/module/core/settings/index/general\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "bc_current_currency": "", "success": "Settings Saved"}, "request": {"path_info": "/admin/module/core/settings/store/general", "status_code": "<pre class=sf-dump id=sf-dump-1165757165 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1165757165\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1870967674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1870967674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-68754195 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>site_title</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mazar Travel</span>\"\n  \"<span class=sf-dump-key>site_desc</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">m/d/Y</span>\"\n  \"<span class=sf-dump-key>site_timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n  \"<span class=sf-dump-key>site_first_day_of_the_weekin_calendar</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>site_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>site_enable_multi_lang</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>home_page_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>logo_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">229</span>\"\n  \"<span class=sf-dump-key>site_favicon</span>\" => \"<span class=sf-dump-str title=\"3 characters\">228</span>\"\n  \"<span class=sf-dump-key>topbar_left_text</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"211 characters\">&lt;div class=&quot;socials&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"211 characters\">&lt;a href=&quot;#&quot;&gt;&lt;i class=&quot;fa fa-facebook&quot;&gt;&lt;/i&gt;&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"211 characters\">&lt;a href=&quot;#&quot;&gt;&lt;i class=&quot;fa fa-linkedin&quot;&gt;&lt;/i&gt;&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"211 characters\">&lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"211 characters\">&lt;span class=&quot;line&quot;&gt;&lt;/span&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"211 characters\">&lt;a href=&quot;mailto:<EMAIL>&quot;&gt;<EMAIL>&lt;/a&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>list_widget_footer</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">NEED HELP?</span>\"\n      \"<span class=sf-dump-key>size</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>content</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"797 characters\">&lt;div class=&quot;contact&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;div class=&quot;c-title&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            Call Us<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;div class=&quot;sub&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            + 00 222 44 5678<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">    &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">    &lt;div class=&quot;contact&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;div class=&quot;c-title&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            Email for Us<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;div class=&quot;sub&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            <EMAIL><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">    &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">    &lt;div class=&quot;contact&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;div class=&quot;c-title&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            Follow Us<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;div class=&quot;sub&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            &lt;a href=&quot;#&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">                &lt;i class=&quot;icofont-facebook&quot;&gt;&lt;/i&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            &lt;a href=&quot;#&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">               &lt;i class=&quot;icofont-twitter&quot;&gt;&lt;/i&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            &lt;a href=&quot;#&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">                &lt;i class=&quot;icofont-youtube-play&quot;&gt;&lt;/i&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"797 characters\">    &lt;/div&gt;</span>\n        \"\"\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">COMPANY</span>\"\n      \"<span class=sf-dump-key>size</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>content</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"220 characters\">&lt;ul&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"220 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;About Us&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"220 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Community Blog&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"220 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Rewards&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"220 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Work with Us&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"220 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Meet the Team&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"220 characters\">&lt;/ul&gt;</span>\n        \"\"\"\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">SUPPORT</span>\"\n      \"<span class=sf-dump-key>size</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>content</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"216 characters\">&lt;ul&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"216 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Account&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"216 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Legal&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"216 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Contact&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"216 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Affiliate Program&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"216 characters\">    &lt;li&gt;&lt;a href=&quot;#&quot;&gt;Privacy Policy&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"216 characters\">&lt;/ul&gt;</span>\n        \"\"\"\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">SETTINGS</span>\"\n      \"<span class=sf-dump-key>size</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>content</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"83 characters\">&lt;ul&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"83 characters\">&lt;li&gt;&lt;a href=&quot;#&quot;&gt;Setting 1&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"83 characters\">&lt;li&gt;&lt;a href=&quot;#&quot;&gt;Setting 2&lt;/a&gt;&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"83 characters\">&lt;/ul&gt;</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>footer_text_left</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&lt;p&gt;Copyright &amp;copy; 2025 by Mazar Travel&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>footer_text_right</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&lt;p&gt;Mazar Travel&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>page_contact_title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">We&#039;d love to hear from you</span>\"\n  \"<span class=sf-dump-key>page_contact_sub_title</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Send us a message and we&#039;ll respond as soon as possible</span>\"\n  \"<span class=sf-dump-key>page_contact_desc</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"199 characters\">&lt;h3&gt;Mazar Travel&lt;/h3&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;&amp;nbsp;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;&amp;nbsp;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;&amp;nbsp;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;&amp;nbsp;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;&amp;nbsp;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;Tell. + 00 216 22334455&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;Email. <EMAIL>&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"199 characters\">&lt;p&gt;KHZEMA SOUSSE 4071 Tunisie&lt;/p&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>page_contact_image</span>\" => \"<span class=sf-dump-str>9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68754195\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3871</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"70 characters\">http://localhost/mazar/public/admin/module/core/settings/index/general</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IkRheTU4RnV0Q3p0akNxaDhWM2VNa3c9PSIsInZhbHVlIjoickFxY0ZHa0lHeGUrclFpUnhOSmlPeTE1UFpIWHl4L0tWY3ZWaVljZEQvSHBnZStWWEtVbXpkNTZoaVNYWnFFNDBGcUM5NmJJdzgwTk5BMWkwQnhSQ3JhUkd6S25pbGlUUFdDSkdQaVgwakhKaWdaNTg4bEFDd3AwVm9xWWFXTVAiLCJtYWMiOiI4YjQxZjc1Yjg0NzcxY2IyMTBlOGJjMmFhOGIyZTM4MWZlYTg3YTFiMDk4MjdjYWMwOGNjOTNiMzIwNDQyZjk1IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IkRkdDRLbEd4OEpCUXdEbVBFbExqcVE9PSIsInZhbHVlIjoiWkdFRkdXdS92MXZ6eHA3ZmZyL1RDeGIyV3JNSDVWZG9zY2gzNE9pdVpnS0p1bHhONUNmbUJxSjF0WkxZdjBQbEtwU2hiVmhIZDZPVFF3aTFLZ0VHRlR3S1pJYzRnUU1PamF3cTJRYURndHdGS0cvQ0R4TlpJbFh6WFJESGVLNjYiLCJtYWMiOiI4N2E4NjVkOTY2MzE4OTEyZWU2ODdjYzhjZTk0ZmQ5MTY2MTFjYjI0YTI4NDRmNmE4MzY2YTI3YTI2NDg3YWU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:36:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"70 characters\">http://localhost/mazar/public/admin/module/core/settings/index/general</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVEcGpMaWtYNlp1STB0MVF6aXIwTHc9PSIsInZhbHVlIjoiSS9KcGZ3ZElHRlo3Y3BhQXJoNmU3QnhlM0tvWmVhWUJZOWVFRGZYZTJGNHEvaUFlV0hsSG5zRTJYZGJGaGYwL05vQjhBTXhXVldLV2o5bUloS201T2xaNHc5UmNZZVZiT3poRVM1Z1ZrU1lRdHF6cjhhMmdaVnZINW5aaUMzUWQiLCJtYWMiOiJkZGQzNWE5NGYyNDEzMzFiNGM5OWMwMmI3NWJlNzFlNjMwZGRiYWZkZjQ2ZWVlNGJmYTVmZTg5MWRhNGNkMDdjIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:36:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjBVeXBIN001TGVWS0JkK1hVbWdGQkE9PSIsInZhbHVlIjoiT1FlejNHSXhybkhIaVdjdXZrbDg5bGtXUmg2TVloWWZoeGRKb0VJWS9qTFJSTEFlcFFTZU9ETjIvdERTODRuNjZ0QUJNaFBQMzFYS0I2cHJZM09jNmdXcWw5SC9uRWZIU3lLUTM4U0p4U1JKc0o5dnRwdkhkRUZTWXZncW5meDQiLCJtYWMiOiI0MDdjODlhYzZmZjU5ZjgzMzM0ZmM2MWVlOGI3ODExYjMxYjc0NGVkMGUwNjI5OTMxM2NlYTQxMmE0Y2RhOWQxIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:36:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVEcGpMaWtYNlp1STB0MVF6aXIwTHc9PSIsInZhbHVlIjoiSS9KcGZ3ZElHRlo3Y3BhQXJoNmU3QnhlM0tvWmVhWUJZOWVFRGZYZTJGNHEvaUFlV0hsSG5zRTJYZGJGaGYwL05vQjhBTXhXVldLV2o5bUloS201T2xaNHc5UmNZZVZiT3poRVM1Z1ZrU1lRdHF6cjhhMmdaVnZINW5aaUMzUWQiLCJtYWMiOiJkZGQzNWE5NGYyNDEzMzFiNGM5OWMwMmI3NWJlNzFlNjMwZGRiYWZkZjQ2ZWVlNGJmYTVmZTg5MWRhNGNkMDdjIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:36:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjBVeXBIN001TGVWS0JkK1hVbWdGQkE9PSIsInZhbHVlIjoiT1FlejNHSXhybkhIaVdjdXZrbDg5bGtXUmg2TVloWWZoeGRKb0VJWS9qTFJSTEFlcFFTZU9ETjIvdERTODRuNjZ0QUJNaFBQMzFYS0I2cHJZM09jNmdXcWw5SC9uRWZIU3lLUTM4U0p4U1JKc0o5dnRwdkhkRUZTWXZncW5meDQiLCJtYWMiOiI0MDdjODlhYzZmZjU5ZjgzMzM0ZmM2MWVlOGI3ODExYjMxYjc0NGVkMGUwNjI5OTMxM2NlYTQxMmE0Y2RhOWQxIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:36:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"70 characters\">http://localhost/mazar/public/admin/module/core/settings/index/general</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Settings Saved</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}