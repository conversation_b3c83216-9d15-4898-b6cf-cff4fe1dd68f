{"The provided password does not match your current password.": "", "-- Select --": "-- Auswählen --", ":num mins": ":num Min", ":num min": ":num Min", ":num hours": ":num Std", ":num hour": ":num Std", ":num days": ":num Tage", ":num day": ":num Tag", ":num weeks": ":num <PERSON><PERSON>en", ":num week": ":num W<PERSON>e", ":num months": ":num Monate", ":num month": ":num <PERSON><PERSON>", ":num years": ":num Jahre", ":num year": ":num Jahr", "sqft": "m²", "Draft": "<PERSON><PERSON><PERSON><PERSON>", "Unpaid": "Unbezahlt", "Paid": "Be<PERSON>hlt", "Processing": "In Bearbeitung", "Completed": "Abgeschlossen", "Confirmed": "Bestätigt", "Cancelled": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "Abbrechen", "Pending": "<PERSON><PERSON><PERSON><PERSON>", "Partial Payment": "Teilzahlung", "Failed": "Fehlgeschlagen", "Phone": "Telefon", "Number": "<PERSON><PERSON><PERSON>", "Email": "E-Mail", "Attachment": "<PERSON><PERSON>", "Multi Attachments": "<PERSON><PERSON><PERSON>", "Text": "Text", "D": "T", "H": "S", ":count Days": ":count <PERSON><PERSON>", ":count Day": ":count Tag", ":count Hours": ":count <PERSON><PERSON><PERSON>", ":count Hour": ":count <PERSON><PERSON><PERSON>", "Show on the map": "Au<PERSON> der Karte anzeigen", "Login": "Anmelden", "Can not authorize": "Autorisierung nicht möglich", "Email :email exists. Can not register new account with your social email": "E-Mail :email existiert bereits. Neues Konto mit Ihrer Social-Media-E-Mail kann nicht registriert werden", "User blocked": "<PERSON><PERSON><PERSON> g<PERSON>", "Your account has been blocked": "<PERSON><PERSON> Konto wurde gesperrt", "Sign Up": "Registrieren", "News": "Nachrichten", "Access denied for user!. Please check your configuration.": "Zugriff für Benutzer verweigert! Bitte überprüfen Sie Ihre Konfiguration.", "Yes! Successfully connected to the DB: \".DB::connection()->getDatabaseName()));\r\n            }else{\r\n                return $this->sendSuccess(false , __(\"Could not find the database. Please check your configuration.": "Ja! Erfolgreich mit der Datenbank verbunden: \".DB::connection()->getDatabaseName()));\r\n            }else{\r\n                return $this->sendSuccess(false , __(\"Datenbank nicht gefunden. Bitte überprüfen Sie Ihre Konfiguration.", "For security, please change your password to continue": "Aus Sicherheitsgründen ändern Sie bitte Ihr Passwort, um fortzufahren", "Upgrade for :price": "Upgrade für :price", "Please verify captcha.": "Bitte Captcha verifizieren.", "Publish": "Veröffentlichen", "Blocked": "<PERSON><PERSON><PERSON><PERSON>", "Manage Agencies": "<PERSON><PERSON> ver<PERSON>", "All": "Alle", "Item not found": "Element nicht gefunden", "Create Agency": "<PERSON><PERSON> er<PERSON>", "Create a Agency": "Eine Agentur erstellen", "Agent only belong one agencies": "Agent g<PERSON><PERSON><PERSON> nur zu einer Agentur", "Agency updated": "Agentur aktualisiert", "Agency created": "<PERSON><PERSON> er<PERSON>", "No items selected!": "Keine Elemente ausgewählt!", "Please select an action!": "Bitte wählen Si<PERSON> eine Aktion!", "Delete success!": "Erfolgreich gelöscht!", "Update success!": "Erfolgreich aktualisiert!", "System error": "<PERSON><PERSON><PERSON>", "Title": "Titel", "Sub Title": "Untertitel", "List Item(s)": "Listenelement(e)", "Name": "Name", "Type": "<PERSON><PERSON>", "Avatar Image": "Avatar-Bild", "Our Team": "Unser Team", "Image": "Bild", "Our Partners": "Unsere Partner", "Agent Register Form": "Agent-Registrierungsformular", "Our Agencies": "Unsere Agenturen", "Manage Agency": "<PERSON><PERSON> ver<PERSON><PERSON>", "Agency not found!": "Agentur nicht gefunden!", "Edit Agency :name": "Agentur :name bearbeiten", "Edit Agency": "<PERSON><PERSON> bearbeiten", "Agency fail": "Agentur<PERSON><PERSON><PERSON>", "Manage Agent": "Agent ve<PERSON><PERSON><PERSON>", "Agency :name": "Agentur :name", "All Agent": "Alle Agenten", "Email is required field": "E-Mail ist ein Pflichtfeld", "Email invalidate": "E-Mail ungültig", "Password is required field": "Passwort ist ein Pflich<PERSON>feld", "The first name is required field": "Der Vorname ist ein Pflichtfeld", "The last name is required field": "Der Nachname ist ein Pflichtfeld", "The business name is required field": "Der Firmenname ist ein Pflichtfeld", "Can not register": "Registrierung nicht möglich", "Register success": "Registrierung erfolgreich", "Register success. Please wait for admin approval": "Registrierung erfolgreich. Bitte warten Sie auf die Admin-Genehmigung", "Success": "Erfolgreich", "Find Agents": "<PERSON><PERSON> finden", "Thank you for contacting us! We will get back to you soon": "Vielen Dank für Ihre Kontaktaufnahme! Wir werden uns bald bei Ihnen melden", "[:site_name] New message": "[:site_name] Neue Nachricht", "Agencies": "<PERSON><PERSON>", " Publish ": " Veröffentlichen ", " Move to Draft ": " Als Entwurf speichern ", " Delete ": " Löschen ", "agent": "Agent", "All Agency": "Alle Agenturen", "Add Agency": "Agentur hinzufü<PERSON>", "Agencies Settings": "Agenturen-Einstellungen", "Agent Settings": "Agent-Einstellungen", "Edit: ": "Bearbeiten: ", "Add new agency": "Neue Agentur hinzufügen", "Permalink": "Permalink", "View agency": "Agentur anzeigen", "Save Changes": "Änderungen speichern", "Author Setting": "Autor-Einstellung", "-- Select User --": "-- <PERSON><PERSON><PERSON> auswählen --", "Feature Image": "Hauptbild", "Agency Content": "Agentur-Inhalt", "Agency name": "Agentur-Name", "Content": "Inhalt", "Office": "<PERSON><PERSON><PERSON>", "Mobile": "Mobil", "Fax": "Fax", "Banner Image": "Banner-Bild", "List Agent": "Agent auflisten", "User": "<PERSON><PERSON><PERSON>", "-- Select Agent --": "-- Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> --", "Add item": "Element hinzufügen", "Locations": "<PERSON><PERSON><PERSON>", "Location": "<PERSON><PERSON>", "-- Please Select --": "-- <PERSON>te auswählen --", "Real address": "<PERSON><PERSON><PERSON> Adress<PERSON>", "The geographic coordinate": "Die geografische Koordinate", "Search by name...": "Nach Name suchen...", "Map Latitude": "Karten-Breitengrad", "Map Longitude": "Karten-Längengrad", "Map Zoom": "Karten-Zoom", "Social Info": "Social-Media-Info", "Name social": "Social-Media-Name", "Code icon": "Icon-Code", "Link social": "Social-Media-Link", "Agencies list": "Agenturen-<PERSON><PERSON>", " Bulk Actions ": " Massenaktionen ", "Do you want to delete?": "Möchten Sie löschen?", "Apply": "<PERSON><PERSON><PERSON>", "Search by name": "Nach Name suchen", "Search": "<PERSON><PERSON>", "Found :total items": ":total Elemente gefunden", "Author": "Autor", "Status": "Status", "Reviews": "Bewertungen", "Date": "Datum", "[Author Deleted]": "[<PERSON><PERSON>]", "Edit": "<PERSON><PERSON><PERSON>", "No data": "<PERSON><PERSON>", "Page Search": "Seitensuche", "Config page search of your website": "Seitensuche Ihrer Website konfigurieren", "General Options": "Allgemeine Optionen", "Title Page": "Titel-Seite", "Review Options": "Bewertungsoptionen", "Config review for agency": "Bewertung für Agentur konfigurieren", "Enable review system for Agency?": "Bewertungssystem für Agentur aktivieren?", "Yes, please enable it": "<PERSON><PERSON>, bitte aktivieren", "Turn on the mode for reviewing agency": "Modus für Agentur-Bewertung aktivieren", "Review must be approval by admin": "Bewertung muss vom Admin genehm<PERSON>t werden", "Yes please": "<PERSON><PERSON> bitte", "ON: Review must be approved by admin - OFF: Review is automatically approved": "AN: Bewertung muss vom Admin genehmigt werden - AUS: Bewertung wird automatisch genehmigt", "Review number per page": "Anzahl Bewertungen pro Seite", "Break comments into pages": "Kommentare auf Seiten aufteilen", "Agent Register": "Agent-Regis<PERSON><PERSON><PERSON>", "Agent Auto Approved?": "Agent automatisch genehmigt?", "Agent Role": "Agent-<PERSON><PERSON>", "You can edit on main lang.": "<PERSON><PERSON> können in der Hauptsprache bearbeiten.", "Agent Profile": "Agent-Profil", "Show agent email in profile?": "Agent-E-Mail im Profil anzeigen?", "Show agent phone in profile?": "Agent-Telefon im Profil anzeigen?", "Content Email Agent Registered": "E-Mail-Inhalt Agent registriert", "Content email send to Agent or Administrator when user registered.": "E-Mail-Inhalt an Agent oder Administrator senden, wenn Benutzer registriert wird.", "Enable send email to customer when customer registered ?": "E-Mail an Kunden senden, wenn Kunde registriert wird?", "You must enable on main lang.": "<PERSON><PERSON> müssen in der Hauptsprache aktivieren.", "Email to agent subject": "E-Mail an Agent Betreff", "Email to agent content": "E-Mail an Agent Inhalt", "Enable send email to Administrator when customer registered ?": "E-Mail an Administrator senden, wenn Kunde registriert wird?", "Email to Administrator subject": "E-Mail an Administrator <PERSON><PERSON><PERSON>", "Email to Administrator content": "E-Mail an Administrator Inhalt", "Config review for agent": "Bewertung für Agent konfigurieren", "Enable review system for Agent?": "Bewertungssystem für Agent aktivieren?", "Turn on the mode for reviewing agent": "Modus für Agent-Bewertung aktivieren", "Hello Administrator": "Hallo Administrator", "Here are new contact information:": "Hier sind neue Kontaktinformationen:", "Message": "Nachricht", "Password is not correct": "Passwort ist nicht korrekt", "You are not allowed to register": "Sie dürfen sich nicht registrieren", "The terms and conditions field is required": "Das Feld für Geschäftsbedingungen ist erforderlich", "Register successfully": "Erfolgreich registriert", "The email field is required": "Das E-Mail-Feld ist erforderlich", "Update successfully": "Erfolgreich aktualisiert", "Successfully logged out": "Erfolgreich abgemeldet", "Current password is not correct": "Aktuelles Passwort ist nicht korrekt", "Password updated. Please re-login": "Passwort aktualisiert. Bitte melden Sie sich erneut an", "Booking not found!": "Buchung nicht gefunden!", "You do not have permission to access": "Sie haben keine Zugriffsberechtigung", "Booking Details": "Buchungsdetails", "Location ID is not available": "Standort-ID ist nicht verfügbar", "Location not found": "Standort nicht gefunden", "News not found": "Nachrichten nicht gefunden", "You have to login in to do this": "<PERSON><PERSON> müssen sich anmelden, um dies zu tun", "Type is required": "<PERSON>p ist erforderlich", "Type does not exists": "<PERSON><PERSON> exist<PERSON>t nicht", "Resource is not available": "Ressource ist nicht verfügbar", "Resource ID is not available": "Ressourcen-ID ist nicht verfügbar", "Resource not found": "Ressource nicht gefunden", "Boat ID is not available": "Boot-ID ist nicht verfügbar", "Mobile App Settings": "Mobile App-Einstellungen", "Mobile Layout": "Mobile Layout", "Choose Layout for Mobile app": "Layout für Mobile App wählen", "Boat": "Boot", "Attributes": "Attribute", "Attributes not found!": "Attribute nicht gefunden!", "Attribute: :name": "Attribut: :name", "Attribute saved": "Attribut g<PERSON><PERSON><PERSON>", "Select at least 1 item!": "Wählen Sie mindestens 1 Element!", "Select an Action!": "W<PERSON>hlen Sie eine Aktion!", "Updated success!": "Erfolgreich aktualisiert!", "Term not found": "Begriff nicht gefunden", "Term: :name": "Begriff: :name", "Term saved": "Begriff g<PERSON>", "Boats": "<PERSON><PERSON>", "Boat Management": "Boot-Verwaltung", "Recovery": "Wiederherstellung", "Recovery Boat Management": "Boot-Verwaltung wiederherstellen", "Add Boat": "<PERSON>ot hinzufü<PERSON>", "Add new Boat": "Neues Boot hinzufügen", "Edit Boat": "<PERSON>ot bearbeiten", "Edit: :name": "Bearbeiten: :name", "DEMO MODE: can not add data": "DEMO-MODUS: Daten können nicht hinzugefügt werden", "Boat updated": "<PERSON><PERSON> aktualisi<PERSON>", "Boat created": "<PERSON>ot erstellt", "Deleted success!": "Erfolgreich gelöscht!", "Permanently delete success!": "Dauerhaft erfolgreich gelöscht!", "Recovery success!": "Wiederherstellung erfolgreich!", "Clone success!": "Klonen erfolgreich!", "Style Background": "Stil-<PERSON><PERSON>grund", "Normal": "Normal", "Slider Boatousel": "Slider<PERSON><PERSON><PERSON><PERSON>", "- Layout Normal: Background Image Uploader": "- Layout Normal: Hi<PERSON>grundbild-Uploader", "- Layout Slider: List Item(s)": "- Layout Slider: Listenelement(e)", "Background Image Uploader": "Hintergrundbild-Uploader", "Service Boat": "Service Boot", "Boat: Form Search": "Boot: Suchformular", "Desc": "Beschreibung", "Number Item": "<PERSON><PERSON><PERSON>", "Style": "Stil", "Slider Carousel": "Slider<PERSON><PERSON><PERSON><PERSON>", "Filter by Location": "<PERSON><PERSON> filtern", "Order": "Reihenfolge", "Date Create": "Erstellungsdatum", "Order By": "Sortieren nach", "ASC": "Aufsteigend", "DESC": "Absteigend", "Only featured items?": "Nur hervorgehobene Elemente?", "List by IDs": "Nach IDs auflisten", "Boat: List Items": "Boot: Elemente auflisten", "Normal Layout": "Normales Layout", "Map Layout": "<PERSON><PERSON>-Layout", "Availability": "Verfügbarkeit", "Boats Availability": "Boot-Verfügbarkeit", "Boat not found": "Boot nicht gefunden", "per Hour: ": "pro Stunde: ", "per Day: ": "pro Tag: ", "Hour: ": "Stunde: ", "Day: ": "Tag: ", "Full Book": "Vollständig gebucht", "You need to return the boat on the same-day": "Sie müssen das Boot am selben Tag zurückgeben", "This boat is not available at selected dates": "<PERSON><PERSON> ist an den ausgewählten Terminen nicht verfügbar", "Update Success": "Aktualisierung erfolgreich", ":count boats found": ":count <PERSON><PERSON> gefunden", ":count boat found": ":count <PERSON><PERSON> gefunden", "Showing :from - :to of :total Boats": "Zeige :from - :to von :total Booten", "Manage Boats": "<PERSON><PERSON> verwalten", "Recovery Boats": "<PERSON><PERSON> wiederherstellen", "Restore boat success!": "Boot erfolgreich wiederhergestellt!", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create Boats": "<PERSON><PERSON> erstellen", "Boat not found!": "Boot nicht gefunden!", "Edit Boats": "Boote bearbeiten", "Delete boat success!": "Boot erfolgreich gelöscht!", "No item!": "Kein <PERSON>!", "Not Found": "Nicht gefunden", "Update success": "Aktualisierung erfolgreich", "Update fail!": "Aktualisierung fehlgeschlagen!", "Search for Boats": "<PERSON>ch Booten suchen", "Can not check availability": "Verfügbarkeit kann nicht überprüft werden", "Your selected dates are not valid": "Ihre ausgewählten Termine sind nicht gültig", "You must book the service for :number days in advance": "Sie müssen den Service :number Tage im Voraus buchen", "You haven't selected return day or hours": "Sie haben keinen Rückgabetag oder -stunden ausgewählt", "Start time booking: :time": "Buchungsstart: :time", "End time booking: :time": "Buchungsende: :time", "You need return boat on same-day": "Sie müssen das Boot am selben Tag zurückgeben", "Please select": "Bitte auswählen", "day": "Tag", "hour": "Stunde", "guest": "Gas<PERSON>", "Not rated": "<PERSON>cht bewertet", ":number Boats": ":number Boote", ":number Boat": ":number Boot", "Filter Price": "<PERSON><PERSON> filtern", "Review Score": "Bewertungspunktzahl", "All Boats": "<PERSON><PERSON> Boote", "Manage Boat": "<PERSON><PERSON> verwalten", "Boat Settings": "Boot-Einstellungen", "Add new attribute": "Neues Attribut hinzufügen", "Attribute Content": "Attribut-Inhalt", "Save Change": "Änderung speichern", "Attribute name": "Attribut-Name", "Position Order": "Positionsreihenfolge", "Ex: 1": "Bsp.: 1", "The position will be used to order in the Filter page search. The greater number is priority": "Die Position wird zur Sortierung in der Filter-Seitensuche verwendet. Die höhere Zahl hat Priorität", "Display Type in detail service": "Anzeigetyp im Detail-Service", "Display Left Icon": "Linkes Icon anzeigen", "Display Center Icon": "<PERSON><PERSON>res Icon anzeigen", "Hide in detail service": "<PERSON>m <PERSON>-Service ausblenden", "Enable hide": "Ausblenden aktivieren", "Hide in filter search": "In Filtersuche ausblenden", "Boat Attributes": "Boot-Attribute", "Add Attributes": "Attribute hinzufügen", "Add new": "<PERSON><PERSON>", " Bulk Action ": " Massenaktion ", "All Attributes": "Alle Attribute", "Actions": "Aktionen", "Manage Terms": "<PERSON>g<PERSON><PERSON> verwalten", "Boats Availability Calendar": "Boot-Verfügbarkeitskalender", "Showing :from - :to of :total boats": "Zeige :from - :to von :total Booten", "No boats found": "<PERSON><PERSON> gefunden", "Date Information": "Datumsinformationen", "Date Ranges": "Datumsbereiche", "Available for booking?": "Für Buchung verfügbar?", "Price per hour": "Preis pro Stunde", "Price per day": "Preis pro Tag", "Close": "Schließen", "Save changes": "Änderungen speichern", "Today": "<PERSON><PERSON>", "Boat Content": "Boot-Inhalt", "Youtube Video": "YouTube-Video", "Youtube link video": "YouTube-Video-Link", "FAQs": "Häufig gestellte Fragen", "Eg: When and where does the tour end?": "Z.B.: Wan<PERSON> und wo endet die Tour?", "Eg: Can I bring my pet?": "Z.B.: Kann ich mein Haustier mitbringen?", "Gallery": "Galerie", "Extra Info": "Zusätzliche Informationen", "Guest": "Gas<PERSON>", "Example: 3": "Beispiel: 3", "Cabin": "<PERSON><PERSON>", "Example: 5": "Beispiel: 5", "Length": "<PERSON><PERSON><PERSON>", "Example: 30m": "Beispiel: 30m", "Speed": "Geschwindigkeit", "Example: 25km/h": "Beispiel: 25km/h", "Specs": "Spezifikationen", "Eg: Range": "Z.B.: Reichweite", "Eg: 6000km": "Z.B.: 6000km", "Cancellation Policy": "Stornierungsrichtlinie", "Full refund up to 4 days prior.": "Vollständige Rückerstattung bis 4 Tage im Voraus.", "Additional Terms & Information": "Zusätzliche Bedingungen & Informationen", "For Sanitary purposes ONLY, although there is a working toilet and shower, we've deactivated the shower and the toliet is for limited use (urine only..pardon the graphic detail!)...": "Nur für sanitäre Z<PERSON>cke, obwohl es eine funktionierende Toilette und Dusche gibt, haben wir die Dusche deaktiviert und die Toilette ist nur begrenzt nutzbar (nur Urin... entschuldigen Sie die grafischen Details!)...", "Include": "Inbegriffen", "Eg: Specialized bilingual guide": "Z.B.: Spezialisierter zweisprachiger Führer", "Exclude": "Ausgeschlossen", "Eg: Additional Services": "Z.B.: Zusätzliche Services", "Loading...": "Lädt...", "Pricing": "Preisgestaltung", "Minimum advance reservations": "Mindest-Vorabreservierungen", "Ex: 3": "Bsp.: 3", "Leave blank if you dont need to use the min day option": "<PERSON><PERSON>, wenn <PERSON>e die Mindest-Tage-Option nicht benötigen", "Start time booking": "Buchungsstart", "End time booking": "Buchungsende", "*Leave it blank if don't use these fields. The end-time must be larger than start-time": "*<PERSON><PERSON>, wenn <PERSON>e diese Felder nicht verwenden. Die Endzeit muss größer als die Startzeit sein", "Enable extra price": "Zusatzpreis aktivieren", "Extra Price": "Zusatzpreis", "Price": "Pre<PERSON>", "Extra price name": "Zusatzpreis-Name", "One-time": "Ein<PERSON>ig", "Service fee": "Servicegebühr", "Enable service fee": "Servicegebühr aktivieren", "Buyer Fees": "Käufergebühren", "Fee name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fee desc": "Gebührenbeschreibung", "Fixed": "Fest", "Percent": "Prozent", "Price per person": "Preis pro Person", "Add new boat": "Neues Boot hinzufügen", "View Boat": "<PERSON><PERSON> anzeigen", "Boat Featured": "Boot hervorgehoben", "Enable featured": "Hervorhebung aktivieren", "Is Instant Booking?": "Ist Sofortbu<PERSON>ng?", "Enable instant booking": "Sofortbuchung aktivieren", "Default State": "Standardzustand", "-- Please select --": "-- <PERSON>te auswählen --", "Always available": "Immer verfügbar", "Only available on specific dates": "<PERSON>ur an bestimmten Terminen verfügbar", " Recovery ": " Wiederherstellung ", "Permanently delete": "Dauerhaft löschen", "Move to Pending": "<PERSON><PERSON> verschieben", " Clone ": " Klonen ", "Advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Featured": "Hervorgehoben", "No boat found": "<PERSON><PERSON> gefunden", "Banner Page": "Banner-Seite", "Layout Search": "Layout-<PERSON><PERSON>", "Location Search Style": "Standort-Suchstil", "Autocomplete from locations": "Autovervollständi<PERSON><PERSON> von <PERSON>", "Autocomplete from Gmap Place": "Autovervollständigung von Google Maps-Orten", "Limit item per Page": "Elemente pro Seite begrenzen", "Default: 9": "Standard: 9", "Radius options": "Radius-Optionen", "Miles": "<PERSON><PERSON>", "Km": "Km", "Layout Map Option": "<PERSON>rten-Layout-Option", "Map Left": "Karte links", "Map Right": "<PERSON><PERSON> rechts", "Map Lat Default": "Karten-Breitengrad Standard", "Map Lng Default": "Karten-Längengrad Standard", "Map Zoom Default": "Karten-Zoom Standard", "Get lat - lng in here": "Breitengrad - Längengrad hier abrufen", "Icon Marker in Map": "Icon-Markierung in Karte", "SEO Options": "SEO-Optionen", "Share Facebook": "Facebook teilen", "Share Twitter": "Twitter teilen", "Seo Title": "SEO-Titel", "Enter title...": "Titel eingeben...", "Seo Description": "SEO-Beschreibung", "Enter description...": "Beschreibung eingeben...", "Featured Image": "Hauptbild", "Facebook Title": "Facebook-Titel", "Facebook Description": "Facebook-Beschreibung", "Facebook Image": "Facebook-Bild", "Twitter Title": "Twitter-Titel", "Twitter Description": "Twitter-Beschreibung", "Twitter Image": "Twitter-Bild", "Config review for boat": "Bewertung für Boot konfigurieren", "Enable review system for Boat?": "Bewertungssystem für Boot aktivieren?", "Turn on the mode for reviewing boat": "Modus für Boot-Bewertung aktivieren", "Customer must book a boat before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "ON: Only post a review after booking - Off: Post review without booking": "AN: Bewertung nur nach Buchung - AUS: Bewertung ohne Buchung", "Allow review after making Completed Booking?": "Bewertung nach abgeschlossener Buchung erlauben?", "Pick to the Booking Status, that allows reviews after booking": "Buchungsstatus wählen, der Bewertungen nach Buchung erlaubt", "Leave blank if you allow writing the review with all booking status": "<PERSON><PERSON>, wenn Sie Bewertungen bei allen Buchungsstatus erlauben", "Review criteria": "Bewertungskriterien", "Eg: Service": "Z.B.: Service", "Booking Buyer Fees Options": "Buchungs-Käufergebühren-Optionen", "Config buyer fees for boat": "Käufergebühren für Boot konfigurieren", "Vendor Options": "Anbieter-Optionen", "Vendor config for boat": "Anbieter-Konfiguration für Boot", "Boat created by vendor must be approved by admin": "<PERSON> erstelltes Boot muss vom Admin genehmigt werden", "ON: When vendor posts a service, it needs to be approved by administrator": "AN: <PERSON><PERSON> einen Service postet, muss er vom Administrator gene<PERSON><PERSON><PERSON> werden", "Allow vendor can change their booking status": "Anbieter darf Buchungsstatus ändern", "ON: Vendor can change their booking status": "AN: <PERSON><PERSON><PERSON> kann Buchungsstatus ändern", "Allow vendor can change their booking paid amount": "An<PERSON><PERSON> darf bezahlten Buchungsbetrag ändern", "ON: Vendor can change their booking paid amount": "AN: <PERSON><PERSON><PERSON> kann bezahlten Buchungsbetrag ändern", "Allow vendor can add service fee": "Anbieter darf Servicegebühr hinzufügen", "ON: Vendor can add service fee": "AN: <PERSON><PERSON><PERSON> kann Servicegebühr hinzufügen", "Booking Deposit": "Buchungskaution", "Booking Deposit Options": "Buchungskautions-Optionen", "Deposit Amount": "Kautionsbetrag", "Deposit Fomular": "Kautionsformel", "Default": "Standard", "Deposit amount + Buyer free": "Kautionsbetrag + Käufer frei", "Disable boat module?": "Boot-<PERSON><PERSON><PERSON>?", "Disable boat module": "Boot<PERSON><PERSON><PERSON><PERSON>", "Yes, please disable it": "<PERSON><PERSON>, bitte deaktivieren", "Form Search Fields": "Suchformular-Felder", "Search Criteria": "Suchkriterien", "Search Field": "<PERSON><PERSON>", "Service name": "Service-Name", "Attribute": "Attribut", "-- Select field type --": "-- <PERSON>ldtyp auswählen --", "-- Select Attribute --": "-- Attribut auswählen --", "Size Column 6": "Spaltengröße 6", "Size Column 4": "Spaltengröße 4", "Size Column 3": "Spaltengröße 3", "Size Column 2": "Spaltengröße 2", "Size Column 1": "Spaltengröße 1", "Map Search Fields": "<PERSON><PERSON><PERSON><PERSON>", "Advance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add new term": "Neuen Begriff hinzufügen", "Term Content": "Begriff-Inhalt", "Term name": "Begriff-Name", "Class Icon": "Klassen-Icon", "get icon in <a href=':link_1' target='_blank'>fontawesome.com</a> or <a href=':link_2' target='_blank'>icofont.com</a>": "Icon abrufen unter <a href=':link_1' target='_blank'>fontawesome.com</a> oder <a href=':link_2' target='_blank'>icofont.com</a>", "Ex: fa fa-facebook": "Bsp.: fa fa-facebook", "Upload image size 30px": "Bild hochladen Größe 30px", "All the Term's image are same size": "Alle Begriff-Bilder haben die gleiche Größe", "Add Term": "Begriff hinzufügen", "All Terms": "Alle Begriffe", "Boat information": "Boot-Informationen", "Booking Number": "Buchungsnummer", "Booking Status": "Buchungsstatus", "Payment method": "Zahlungsmethode", "Payment Note": "Zahlungsnotiz", "Boat name": "Boot-Name", "Address": "<PERSON><PERSON><PERSON>", "Start date": "Startdatum", "End date:": "Enddatum:", "Durations:": "Dauer:", "Rental price:": "Mietpreis:", "Extra Prices:": "Zusatzpreise:", "Coupon": "Gutschein", "Total": "Gesamt", "Remain": "Verbleibend", "Manage Bookings": "Buchungen verwalten", "Checkout": "<PERSON><PERSON>", "You have to verify email first": "Sie müssen zuerst Ihre E-Mail verifizieren", "Service not found": "Service nicht gefunden", "Please verify the captcha": "Bitte verifizieren Sie das Captcha", "The password confirmation does not match": "Die Passwort-Bestätigung stimmt nicht überein", "The password must be at least 6 characters": "Das Passwort muss mindestens 6 Zeichen haben", "Your credit balance is :amount": "<PERSON>hr Guthaben beträgt :amount", "Term conditions is required field": "Geschäftsbedingungen sind ein Pflichtfeld", "Payment gateway is required field": "Zahlungsgateway ist ein Pflichtfeld", "Payment gateway not found": "Zahlungsgateway nicht gefunden", "Payment gateway is not available": "Zahlungsgateway ist nicht verfügbar", "You payment has been processed successfully": "Ihre Zahlung wurde erfolgreich verarbeitet", "Service type not found": "Service-<PERSON>p nicht gefunden", "Service is not bookable": "Service ist nicht buchbar", "You cannot book your own service": "Sie können Ihren eigenen Service nicht buchen", "Thank you for contacting us! We will be in contact shortly.": "Vielen Dank für Ihre Kontaktaufnahme! Wir werden uns in Kürze bei Ihnen melden.", "Remain can not smaller than 0": "Verbleibend kann nicht kleiner als 0 sein", "Booking not found": "Buchung nicht gefunden", "You don't have access.": "<PERSON>e haben keinen Zugriff.", "You booking has been changed successfully": "Ihre Buchung wurde erfolgreich geändert", "You got reply from :name": "Sie haben eine Antwort von :name erhalten", "[:site_name] New inquiry has been made": "[:site_name] Neue Anfrage wurde gestellt", "[:site_name] You get new inquiry request": "[:site_name] Sie haben eine neue Anfrage erhalten", "[:site_name] New booking has been made": "[:site_name] Neue Buchung wurde erstellt", "[:site_name] Your service got new booking": "[:site_name] Ihr Service hat eine neue Buchung erhalten", "Thank you for booking with us": "Vielen Dank für Ihre Buchung bei uns", "[:site_name] The booking status has been updated": "[:site_name] Der Buchungsstatus wurde aktualisiert", "Your booking status has been updated": "Ihr Buchungsstatus wurde aktualisiert", "Your payment has been canceled": "Ihre Zahlung wurde storniert", "Thank you, we will contact you shortly": "<PERSON><PERSON>en Dank, wir werden uns in Kürze bei Ihnen melden", "Enable Offline Payment?": "Offline-Zahlung aktivieren?", "Custom Name": "Benutzerdefinierter Name", "Offline Payment": "Offline-Zahlung", "Custom Logo": "Benutzerdefiniertes Logo", "Custom HTML Description": "Benutzerdefinierte HTML-Beschreibung", "Enable Paypal Standard?": "PayPal Standard aktivieren?", "Paypal": "PayPal", "Enable Sandbox Mod?": "Sandbox-Modus aktivieren?", "Convert To": "Umwandeln in", "In case of main currency does not support by PayPal. You must select currency and input exchange_rate to currency that PayPal support": "Falls die Hauptwährung von PayPal nicht unterstützt wird. Sie müssen eine Währung auswählen und den Wechselkurs zu einer von PayPal unterstützten Währung eingeben", "Exchange Rate": "Wechselkurs", "Example: Main currency is VND (which does not support by PayPal), you may want to convert it to USD when customer checkout, so the exchange rate must be 23400 (1 USD ~ 23400 VND)": "Beispiel: Hauptwährung ist VND (die von PayPal nicht unterstützt wird), <PERSON><PERSON> möchten sie beim Checkout in USD umwandeln, daher muss der Wechselkurs 23400 betragen (1 USD ~ 23400 VND)", "Sandbox API Username": "Sandbox API-Benutzername", "Sandbox API Password": "Sandbox API-Passwort", "Sandbox Signature": "Sandbox-Signatur", "API Username": "API-Benutzername", "API Password": "API-Passwort", "Signature": "Signatur", "Booking status does need to be paid": "Buchungsstatus muss bezahlt werden", "Booking total is zero. Can not process payment gateway!": "Buchungsgesamtbetrag ist null. Zahlungsgateway kann nicht verarbeitet werden!", "Payment Failed": "Zahlung fehlgeschlagen", "You cancelled the payment": "Sie haben die Zahlung storniert", "PayPal does not support currency: :name": "PayPal unterstützt die Währung nicht: :name", "Exchange rate to :name must be specific. Please contact site owner": "Wechsel<PERSON><PERSON> zu :name muss spezifisch sein. Bitte kontaktieren Sie den Website-Besitzer", "Enable Payrexx Checkout?": "Payrexx Checkout aktivieren?", "Payrexx Checkout": "Payrexx Checkout", "Instance name": "Instanz-Name", "Api secret key": "API-Geheimschlüssel", "Url callback: ": "URL-Callback: ", "Your payment has been placed": "Ihre Zahlung wurde platziert", "Payment Processing": "Zahlungsverarbeitung", "Payment Failed.": "Zahlung fehlgeschlagen.", "You payment has been processed successfully before": "Ihre Zahlung wurde bereits erfolgreich verarbeitet", "No information found": "<PERSON>ine Informationen gefunden", "referenceId can't null": "Referenz-<PERSON> kann nicht null sein", "Enable Paystack gateway?": "Paystack-Gateway aktivieren?", "Paystack": "Paystack", "Public key": "Öffentlicher Schlüssel", "Secret key": "Geheimer Schlüssel", "Payment Url": "Zahlungs-URL", "Merchant Email": "Händler-E-Mail", "not update status \" . $response['event'])]);\r\n                    }\r\n                    else {\r\n                        return response()->json(['status' => 'error": "Status nicht aktualisiert \" . $response['event'])]);\r\n                    }\r\n                    else {\r\n                        return response()->json(['status' => 'error", "Enable Stripe Checkout V2?": "Stripe Checkout V2 aktivieren?", "Stripe": "Stripe", "Secret Key": "Geheimer Schlüssel", "Publishable Key": "Veröffentlich<PERSON><PERSON>", "Enable Sandbox Mode": "Sandbox-Modus aktivieren", "Test Secret Key": "Test-Geheimschlüssel", "Test Publishable Key": "Test-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Webhook Secret": "Webhook-Geheimnis", "Webhook url: <code>:code</code>": "Webhook-URL: <code>:code</code>", "Webhook error while validating signature.": "Webhook-Fehler beim Validieren der Signatur.", "Payment not found": "Zahlung nicht gefunden", "Received unknown event type": "Unbekannter Ereignistyp erhalten", "Buy credits": "<PERSON><PERSON><PERSON><PERSON> kaufen", ":name has created new Booking": ":name hat eine neue Buchung erstellt", ":name has changed to :status": ":name wurde zu :status geändert", ":name has sent a Enquiry for :title": ":name hat eine Anfrage für :title gesendet", ":name has updated the PAID amount on :title": ":name hat den BEZAHLTEN Betrag für :title aktualisiert", "Administrator has updated the PAID amount on :title": "Administrator hat den BEZAHLTEN Betrag für :title aktualisiert", "Revenue": "Umsatz", "Total revenue": "Gesamtumsatz", "Earning": "<PERSON><PERSON><PERSON>", "Total Earning": "Gesamtverdienst", "Bookings": "Buchungen", "Total bookings": "Gesamtbuchungen", "Services": "Services", "Total bookable services": "Gesamte buchbare Services", "Total Revenue": "Gesamtumsatz", "Total pending": "Gesamt ausstehend", "Earnings": "Verdienste", "Total earnings": "Gesamtverdienste", "Total Pending": "Gesamt ausstehend", "Total Fees": "Gesamtgebühren", "Total Commission": "Gesamtprovision", "Total Booking": "Gesamtbuchung", "Payment fail": "Zahlung fehlgeschlagen", "Transaction not found": "Transaktion nicht gefunden", "Payment updated": "Zahlung aktualisiert", "Plan fail": "Plan fehlgeschlagen", "Plan updated": "Plan aktualisiert", "Booking Settings": "Buchungseinstellungen", "Payment Settings": "Zahlungseinstellungen", "Enquiry Settings": "Anfrage-Einstellungen", "Guest Checkout": "Gast-Checkout", "Enable guest checkout": "Gast-Checkout aktivieren", "Yes, please": "<PERSON>a, bitte", "Enable Ticket/Guest information": "Ticket-/Gast-Informationen aktivieren", "Checkout Page": "Checkout-Seite", "Change your checkout page options": "Ändern Sie Ihre Checkout-Seitenoptionen", "Enable reCapcha Booking Form": "reCaptcha für Buchungsformular aktivieren", "On ReCapcha": "reCaptcha an", "Turn on the mode for booking form": "Modus für Buchungsformular aktivieren", "Terms & Conditions page": "Geschäftsbedingungen-Seite", "Invoice Page": "Rechnungsseite", "Change your invoice page options": "Ändern Sie Ihre Rechnungsseitenoptionen", "Invoice Logo": "Rechnungslogo", "Invoice Company Info": "Rechnungs-Firmeninfo", "Settings Enquiry for Service": "Anfrage-Einstellungen für Service", "Change your enquiry options": "Ändern Sie Ihre Anfrage-Optionen", "Enable enquiry for Hotel": "Anfrage für Hotel aktivieren", "Enable enquiry form": "Anfrageformular aktivieren", "Enquiry Type": "Anfrage-Typ", "Booking & Enquiry": "Buchung & Anfrage", "Only Enquiry": "<PERSON><PERSON> Anfrage", "Enable enquiry for Tour": "Anfrage für Tour aktivieren", "Enable enquiry for Space": "Anfrage für Raum aktivieren", "Enable enquiry for Car": "Anfrage für Auto aktivieren", "Enable enquiry for Event": "Anfrage für Event aktivieren", "Enable enquiry for Boat": "Anfrage für Boot aktivieren", "Settings Enquiry": "Anfrage-Einstellungen", "Enable re-catpcha for enquiry?": "reCaptcha für Anfrage aktivieren?", "Enable re-captcha at enquiry form": "reCaptcha im Anfrageformular aktivieren", "Settings Email Enquiry": "E-Mail-Anfrage-Einstellungen", "Change your email enquiry options": "Ändern Sie Ihre E-Mail-Anfrage-Optionen", "Enable send email to Vendor": "E-Mail an Anbieter senden aktivieren", "Email to Vendor content": "E-Mail an Anbieter Inhalt", "Enable send email to Administrator": "E-Mail an Administrator senden aktivieren", "Currency": "Währung", "Currency Format": "Währungsformat", "Main Currency": "Hauptwährung", "Format": "Format", "Right (100$)": "Rechts (100$)", "Right with space (100 $)": "Re<PERSON>s mit Leerzeichen (100 $)", "Left ($100)": "Links ($100)", "Left with space ($ 100)": "<PERSON><PERSON> mit Leerzeichen ($ 100)", "Thousand Separator": "Tausendertrennzeichen", "Decimal Separator": "Dezimaltrennzeichen", "No. of Decimals": "<PERSON><PERSON><PERSON>", "Extra Currency": "Zusätzliche Währung", "Sub Currency": "Unterwährung", "Exchange rate": "Wechselkurs", "Example: Main currency is VND, and the extra currency is USD, so the exchange rate must be 23400 (1 USD ~ 23400 VND)": "Beispiel: Hauptwährung ist VND und die zusätzliche Währung ist USD, daher muss der Wechselkurs 23400 betragen (1 USD ~ 23400 VND)", "Payment Gateways": "Zahlungsgateways", "You can enable and disable your payment gateways here": "Sie können hier Ihre Zahlungsgateways aktivieren und deaktivieren", "Your Information": "<PERSON><PERSON>e <PERSON>en", "First name": "<PERSON><PERSON><PERSON>", "Last name": "Nachname", "Address line 1": "Adresszeile 1", "Address line 2": "Adresszeile 2", "City": "Stadt", "State/Province/Region": "Bundesland/Provinz/Region", "ZIP code/Postal code": "PLZ/Postleitzahl", "Country": "Land", "Special Requirements": "Besondere Anforderungen", "Credit want to pay?": "<PERSON><PERSON> be<PERSON>?", "Credit": "<PERSON><PERSON><PERSON><PERSON>", "Pay now": "Jetzt bezahlen", "How do you want to pay?": "Wie möchten Sie bezahlen?", "Pay deposit": "<PERSON><PERSON> be<PERSON>en", "Pay in full": "Vollständig bezahlen", "Create a new account?": "Neues Konto erstellen?", "First Name": "<PERSON><PERSON><PERSON>", "Last Name": "Nachname", "<EMAIL>": "<EMAIL>", "Your Phone": "Ihr Telefon", "Password": "Passwort", "Password confirmation": "Passwort-Bestätigung", "Your City": "<PERSON><PERSON><PERSON> St<PERSON>t", "I have read and accept the": "Ich habe gelesen und akzeptiere die", "terms and conditions": "Geschäftsbedingungen", "Submit": "<PERSON><PERSON><PERSON><PERSON>", "Select Payment Method": "Zahlungsmethode auswählen", "Booking Submission": "Buchungsübermittlung", "your booking was submitted successfully!": "Ihre Buchung wurde erfolgreich übermittelt!", "Booking details has been sent to:": "Buchungsdetails wurden gesendet an:", "Booking Date": "Buchungsdatum", "Payment Method": "Zahlungsmethode", "Booking History": "Buchungsverlauf", "Name on the Card": "Name auf der <PERSON>", "Card Name": "<PERSON><PERSON><PERSON>", "Card Number": "Kartennummer", "Expiration": "Ablaufdatum", "CVC": "CVC", "Enquiry": "Anfrage", "Name *": "Name *", "Email *": "E-Mail *", "Note": "Notiz", "Send now": "Jetzt senden", "Car": "Auto", "Cars": "Autos", "Car Management": "Auto-Verwaltung", "Recovery Car Management": "Auto-Verwaltung wiederherstellen", "Add Car": "Auto hinzufügen", "Add new Car": "Neues Auto hinzufügen", "Edit Car": "Auto bearbeiten", "Car updated": "Auto aktualisiert", "Car created": "Auto erstellt", "Select term car": "Auto-Begriff auswählen", "Service Car": "Service Auto", "Car: Term Featured Box": "Auto: Begriff-Hervorhebungsbox", "Car: Form Search": "Auto: Suchformular", "Car: List Items": "Auto: Elemente auflisten", "Cars Availability": "Auto-Verfügbarkeit", "Car not found": "Auto nicht gefunden", ":count cars found": ":count Autos gefunden", ":count car found": ":count Auto gefunden", "Showing :from - :to of :total Cars": "Zeige :from - :to von :total Autos", "Manage Cars": "Autos verwalten", "Recovery Cars": "Autos wiederherstellen", "Restore car success!": "Auto erfolgreich wiederhergestellt!", "Create Cars": "Autos erstellen", "Car not found!": "Auto nicht gefunden!", "Edit Cars": "Autos bearbeiten", "Delete car success!": "Auto erfolgreich gelöscht!", "Search for Cars": "Nach Autos suchen", "This car is not available at selected dates": "Dieses Auto ist an den ausgewählten Terminen nicht verfügbar", "You must to book a minimum of :number days": "<PERSON>e müssen mindestens :number Tage buchen", "Please select date!": "Bitte Datum auswählen!", ":number Cars": ":number Autos", ":number Car": ":number Auto", "All Cars": "Alle Autos", "Manage Car": "Auto verwalten", "Car Settings": "Auto-Einstellungen", "Car Attributes": "Auto-Attribute", "Cars Availability Calendar": "Auto-Verfügbarkeitskalender", "Showing :from - :to of :total cars": "Zeige :from - :to von :total Autos", "No cars found": "Keine Autos gefunden", "Instant Booking?": "Sofortbuchung?", "Car Content": "Auto-Inhalt", "Passenger": "<PERSON><PERSON><PERSON>", "Gear Shift": "Gangschaltung", "Example: Auto": "Beispiel: Automatik", "Baggage": "<PERSON><PERSON><PERSON><PERSON>", "Door": "<PERSON><PERSON><PERSON>", "Example: 4": "Beispiel: 4", "Ical": "iCal", "Import url": "Import-URL", "Export url": "Export-URL", "Car Number": "Auto-Nummer", "Car Price": "Auto-Preis", "Sale Price": "Verkaufspreis", "Car Sale Price": "Auto-Verkaufspreis", "If the regular price is less than the discount , it will show the regular price": "Wenn der reguläre Preis niedriger als der Rabatt ist, wird der reguläre Preis angezeigt", "Minimum day stay requirements": "Mindest-Aufenthaltsdauer-Anforderungen", "Ex: 2": "Bsp.: 2", "Leave blank if you dont need to set minimum day stay option": "<PERSON><PERSON>, wenn <PERSON><PERSON> die Mindest-Aufenthaltsdauer-<PERSON><PERSON> nicht benötigen", "Per day": "Pro Tag", "Add new car": "Neues Auto hinzufügen", "View Car": "Auto anzeigen", "Car Featured": "Auto hervorgehoben", "No car found": "Kein Auto gefunden", "Config review for car": "Bewertung für Auto konfigurieren", "Enable review system for Car?": "Bewertungssystem für Auto aktivieren?", "Turn on the mode for reviewing car": "Modus für Auto-Bewertung aktivieren", "Customer must book a car before writing a review?": "Kunde muss Auto buchen, bevor er eine Bewertung schreibt?", "Config buyer fees for car": "Käufergebühren für Auto konfigurieren", "Vendor config for car": "Anbieter-Konfiguration für Auto", "Car created by vendor must be approved by admin": "Von <PERSON> erstelltes Auto muss vom Admin genehmigt werden", "Disable car module?": "Auto-Mo<PERSON>l <PERSON>?", "Disable car module": "Auto-Mo<PERSON>l <PERSON>", "Car information": "Auto-Informationen", "Car name": "Auto-Name", "Days:": "Tage:", "Adults": "Erwachsene", "Children": "Kinder", ":count day": ":count Tag", ":count days": ":count <PERSON><PERSON>", "Contact Submissions": "Kontakt-Übermittlungen", "Please select at least 1 item!": "Bitte wählen Sie mindestens 1 Element!", "No Action is selected!": "Keine Aktion ausgewählt!", "Class Block": "Klassen-Block", "Other Block": "Anderer Block", "Contact Block": "Kontakt-Block", "Contact Page": "Kontakt-Seite", "Contact": "Kontakt", "All Contact Submissions": "Alle Kontakt-Übermittlungen", "Search...": "Suchen...", "SEND MESSAGE": "NACHRICHT SENDEN", "Primary": "<PERSON><PERSON><PERSON><PERSON>", "Footer": "Fußzeile", "Menus": "<PERSON><PERSON><PERSON>", "Create new menu": "Neues Menü er<PERSON>llen", "Page": "Seite", "News Category": "Nachrichten-Kategorie", "Menu not found": "<PERSON>ü nicht gefunden", "You can not edit menu in demo mode": "<PERSON><PERSON> können das Menü im Demo-Modus nicht bearbeiten", "Your menu has been saved": "Ihr Menü wurde gespeichert", "Module Management": "Modul-<PERSON>erwaltung", "All Notifications": "Alle Benachrichtigungen", "DEMO MODE: Disable setting update": "DEMO-MODUS: Einstellungsupdate deaktiviert", "Settings Saved": "Einstellungen gespeichert", "Please enter license key": "Bitte Lizenzschlüssel eingeben", "Can not connect to update server. Please check again": "Verbindung zum Update-Server nicht möglich. Bitte erneut prüfen", "You are using latest version of Booking Core": "Sie verwenden die neueste Version von Booking Core", "Can not get update file from server": "Update-<PERSON><PERSON> kann nicht vom Server abgerufen werden", "Can not download update file to folder storage": "Update-<PERSON><PERSON> kann nicht in den Speicherordner heruntergeladen werden", "Can not un-zip the package": "<PERSON>et kann nicht entpackt werden", "License information has been saved": "Lizenzinformationen wurden gespeichert", "You cant save cookie": "<PERSON>e können Cook<PERSON> nicht speichern", "Clear cache success!": "<PERSON><PERSON> erfolg<PERSON>ich geleert!", "Dashboard": "Dashboard", "Setting": "Einstellung", "installer_messages.environment.wizard.form.name_required": "Name ist erforderlich", "installer_messages.environment.wizard.form.db_connection_failed": "Datenbankverbindung fehlgeschlagen", ":name has created :services :title": ":name hat :services :title erstellt", ":name has created a Review :review on :title": ":name hat eine Bewertung :review für :title erstellt", ":title has been deleted by :by": ":title wurde von :by <PERSON><PERSON><PERSON><PERSON>", ":title was updated to :status by :by": ":title wurde von :by auf :status aktualisiert", "Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "General Settings": "Allgemeine Einstellungen", "Menu": "<PERSON><PERSON>", "Tools": "Werkzeuge", "Modules": "<PERSON><PERSON><PERSON>", "Languages": "<PERSON><PERSON><PERSON>", "Translation Manager": "Übersetzungsmanager", "System Logs": "System-Logs", "System": "System", "Advanced Settings": "Erweiterte Einstellungen", "Style Settings": "Stil-Einstellungen", "Vendor": "<PERSON><PERSON><PERSON>", "-- Vendor --": "-- <PERSON><PERSON><PERSON> --", "-- All Location --": "-- <PERSON><PERSON> --", "-- All --": "-- Alle --", "Only Featured": "<PERSON><PERSON>", "Edit Menu:": "<PERSON><PERSON> bearbeiten:", "Menu name": "Menü-Name", "No items found": "<PERSON>ine Elemente gefunden", "Add to Menu": "Zum Menü hinzufügen", "Custom Url": "Benutzerdefinierte URL", "URL": "URL", "Link Text": "Link-Text", "Menu items": "Menü-Elemente", "Label": "Bezeichnung", "Class": "Klass<PERSON>", "Target": "<PERSON><PERSON>", "Open new tab": "Neuen Tab <PERSON>", "Enable mega menu": "Mega-Menü aktivieren", "Columns": "Spalten", "2 columns": "2 Spalten", "3 columns": "3 Spalten", "4 columns": "4 Spalten", "Mega image url": "Mega-Bild-URL", "Delete": "Löschen", "Origin: ": "Ursprung: ", "Menu Configs": "Menü-Konfigurationen", "Save Menu": "<PERSON>ü s<PERSON>ichern", "Menu Management": "Menü-Verwaltung", "All Menus": "<PERSON><PERSON>", "Use for": "Verwenden für", "All Modules": "Alle Module", "Active": "Aktiv", "Deactivate": "Deaktivieren", "Module name": "Modul-Name", "Description": "Beschreibung", "Version": "Version", "No Module found": "<PERSON><PERSON> gefunden", "Unread": "<PERSON><PERSON><PERSON><PERSON>", "Read": "<PERSON><PERSON><PERSON>", "You don't have any notifications": "Sie haben keine Benachrichtigungen", "Search engine": "Suchmaschine", "Search Engine": "Suchmaschine", "Allow search engines to show this service in search results?": "Suchmas<PERSON>en erlauben, diesen Service in Suchergebnissen anzuzeigen?", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "Leave blank to use service title": "<PERSON><PERSON>, um Service-Tite<PERSON> zu verwenden", "Search Options": "Suchoptionen", "Search open tab": "<PERSON>e in neuem Tab öffnen", "Current Tab": "Aktueller Tab", "Open New Tab": "Neuen Tab <PERSON>", "Square Size Unit": "Quadratische Größeneinheit", "Size Unit": "Größeneinheit", "Square metre (m2)": "Quadratmeter (m²)", "Square feet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map Provider": "Kartenan<PERSON><PERSON>", "Change map provider of your website": "Kartenanbieter Ihrer Website ändern", "OpenStreetMap.org": "OpenStreetMap.org", "Google Map": "Google Maps", "Gmap API Key": "Google Maps API-Schlüssel", "Learn how to get an api key": "<PERSON><PERSON><PERSON><PERSON>, wie Sie einen API-Schlüssel erhalten", "Map Options Default": "Standard-Kartenoptionen", "Map Clustering": "Karten-Clustering", "Off": "Aus", "On": "An", "Map fitBounds": "Karte an Grenzen anpassen", "Social Login": "Social Login", "Change social login information for your website": "Social Login-Informationen für Ihre Website ändern", "Facebook": "Facebook", "Enable Facebook Login?": "Facebook-Login aktivieren?", "Facebook Client Id": "Facebook Client-ID", "Facebook Client Secret": "Facebook Client-Secret", "Google": "Google", "Enable Google Login?": "Google-Login aktivieren?", "Google Client Id": "Google Client-ID", "Google Client Secret": "Google Client-Secret", "Twitter": "Twitter", "Enable Twitter Login?": "Twitter-Login aktivieren?", "Twitter Client Id": "Twitter Client-ID", "Twitter Client Secret": "Twitter Client-Secret", "Captcha": "<PERSON><PERSON>", "ReCaptcha Config": "ReCaptcha-Konfiguration", "Enable ReCaptcha": "ReCaptcha aktivieren", "Version 2": "Version 2", "Version 3": "Version 3", "Api Key": "API-Schlüssel", "Api Secret": "API-Secret", "Custom Scripts for all languages": "Benutzerdefinierte Skripte für alle Sprachen", "Add custom HTML script before and after the content, like tracking code": "Benutzerdefinierte HTML-Skripte vor und nach dem Inhalt hinzufügen, wie Tracking-Code", "Custom Scripts": "Benutzerdefinierte Skripte", "Head Script": "Head-<PERSON><PERSON><PERSON><PERSON>", "scripts before closing head tag": "Skripte vor schließendem Head-Tag", "Body Script": "Body-<PERSON><PERSON><PERSON><PERSON>", "scripts after open of body tag": "Skripte nach öffnendem Body-Tag", "Footer Script": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom Scripts for :name": "Benutzerdefinierte Skripte für :name", "Cookie agreement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cookie agreement config": "Cookie-Vereinbarung-Konfiguration", "-- Select one --": "-- <PERSON><PERSON> ausw<PERSON>hl<PERSON> --", "Style 1": "Stil 1", "Style 2": "Stil 2", "Agree Text Button": "Zustimmungs-Text-<PERSON><PERSON>", "Site Information": "Website-Informationen", "Information of your website for customer and goole": "Informationen Ihrer Website für Kunden und Google", "Site title": "Website-Titel", "Site Desc": "Website-Beschreibung", "Date format": "Datumsformat", "Timezone": "Zeitzone", "-- Default --": "-- Standard --", "Change the first day of week for the calendars": "<PERSON><PERSON>ag für Kalender ändern", "Monday": "Montag", "Sunday": "Sonntag", "Language": "<PERSON><PERSON><PERSON>", "Change language of your websites": "Sprache Ihrer Website ändern", "Select default language": "Standardsprache auswählen", "Manage languages here": "<PERSON><PERSON><PERSON> hier verwalten", "Enable Multi Languages": "Mehrsprachigkeit aktivieren", "Enable": "Aktivieren", "Enable RTL": "RTL aktivieren", "Homepage": "Startseite", "Change your homepage content": "Inhalt Ihrer Startseite ändern", "Page for Homepage": "Seite für Startseite", "Header & Footer Settings": "Kopf- und Fußzeilen-Einstellungen", "Change your options": "Ihre Optionen ändern", "Logo": "Logo", "Favicon": "Favicon", "Topbar Left Text": "Topbar linker Text", "Footer List Widget": "Fußzeilen-Listen-Widget", "Size": "Größe", "Footer Text Left": "Fußzeilen-Text links", "Footer Text Right": "Fußzeilen-Text rechts", "Page contact settings": "Kontaktseiten-Einstellungen", "Settings for contact page": "Einstellungen für Kontaktseite", "Contact title": "Kontakt-Titel", "Contact sub title": "Kontakt-Untertitel", "Contact Desc": "Kontakt-Beschreibung", "Contact Featured Image": "Kontakt-Hauptbild", "Cookie preferences": "Cookie-Einstellungen", "Cookie Settings Modal": "Cookie-Einstellungen-Modal", "Cookie Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Primary button Text": "<PERSON><PERSON><PERSON><PERSON>-Text", "Accept all": "Alle akzeptieren", "Primary button Role": "<PERSON><PERSON><PERSON><PERSON>", "Accept selected": "Ausgewählte akzeptieren", "Secondary button Text": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Text", "Settings": "Einstellungen", "Secondary button Role": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Open modal settings": "Modal-Einstellungen öffnen", "Accept necessary": "Notwendige akzeptieren", "Button save setting text": "Button-Einstellungen-speichern-Text", "Save settings": "Einstellungen speichern", "Button Accept All text": "Button-Alle-akzeptieren-Text", "Accept All": "Alle akzeptieren", "Button Reject All text": "Button-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Text", "Reject All": "<PERSON>e <PERSON>en", "Setting options": "Einstellungsoptionen", "Action": "Aktion", "Toggle": "Umschalten", "Readonly": "<PERSON><PERSON> lesen", "Value": "Wert", "Config Broadcast": "Broadcast konfigurieren", "Change your config broadcast site": "Ihre Broadcast-Website-Konfiguration ändern", "Broadcast Driver": "Broadcast-<PERSON><PERSON><PERSON>", "Pusher API": "Pusher API", "Change your API for pusher here. It will use for chat plugin and notification": "Ändern Sie hier Ihre API für Pusher. Sie wird für Chat-Plugin und Benachrichtigungen verwendet", "Pusher API Information": "Pusher API-Informationen", "API KEY": "API-SCHLÜSSEL", "API Secret": "API-Secret", "APP ID": "APP-ID", "Cluster": "Cluster", "General Style": "Allgemeiner Stil", "Change main color, typo ...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Typografie ändern ...", "Main color": "Hauptfarbe", "Typography": "<PERSON><PERSON><PERSON><PERSON>", "Font Family": "Schriftfamilie", "Color": "Farbe", "Font Size": "Schriftgröße", "Line Height": "Zeilenhöhe", "Font Weight": "Schriftstärke", "bold or 400": "fett oder 400", "H1,H2,H3 Options": "H1,H2,H3-<PERSON><PERSON><PERSON>", "H1 Font Family": "H1-Schriftfamilie", "H2 Font Family": "H2-Schriftfamilie", "H3 Font Family": "H3-Schriftfamilie", "Custom CSS for all languages": "Benutzerdefiniertes CSS für alle Sprachen", "Write your own custom css code": "Schreiben Sie Ihren eigenen benutzerdefinierten CSS-Code", "Custom CSS": "Benutzerdefiniertes CSS", "Custom CSS for :name": "Benutzerdefiniertes CSS für :name", "Config Sms": "SMS konfigurieren", "SMS driver": "SMS-Treiber", "Sms Driver": "SMS-Treiber", "Config Nexmo Driver": "Nexmo-Treiber konfigurieren", "Nexmo Api Key": "Nexmo API-Schlüssel", "Nexmo Api Secret": "Nexmo API-Secret", "From": "<PERSON>", "Config Twilio Driver": "Twilio-Treiber konfigurieren", "Twilio Account Sid": "<PERSON><PERSON><PERSON>-S<PERSON>", "Twilio Account Token": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "SMS Event Booking": "SMS-Event-Buchung", "Phone number must be E.164 format": "Telefonnummer muss im E.164-Format sein", "[+][country code][subscriber number including area code]": "[+][<PERSON><PERSON><PERSON><PERSON>][Teilnehmernummer einschließlich Vorwahl]", "Example": "Beispiel", "Config Phone Administrator": "Administrator-Telefon konfigurier<PERSON>", "Admin Phone": "Admin-Telefon", "Create Booking": "Buchung erstellen", "Administrator": "Administrator", "Customer": "Kunde", "Enable send sms to Administrator when have booking?": "SMS an Administrator senden, wenn Buchung vorhanden?", "Message to Administrator": "<PERSON><PERSON><PERSON><PERSON> an Administrator", "Enable send sms to Vendor when have booking?": "SMS an Anbieter senden, wenn Buchung vorhanden?", "Message to Customer": "Nachricht an Kunde", "Enable send sms to Customer when have booking?": "SMS an Kunde senden, wenn Buchung vorhanden?", "Update booking": "Buchung aktualisieren", "Enable send sms to Administrator when update booking?": "SMS an Administrator senden, wenn Buchung aktualisiert wird?", "Enable send sms to Vendor when update booking?": "SMS an Anbieter senden, wenn Buchung aktualisiert wird?", "Enable send sms to Customer when update booking?": "SMS an Kunde senden, wenn Buchung aktualisiert wird?", "Sms Testing": "SMS-Test", "To (phone number)": "An (Telefonnummer)", "Send Sms Test": "SMS-Test senden", "Main Settings": "Haupteinstellungen", "Modules for Booking Core": "Module für Booking Core", "Manage languages of your website": "Sprachen Ihrer Website verwalten", "Translations": "Übersetzungen", "Translation manager of your website": "Übersetzungsmanager Ihrer Website", "System Log Viewer": "System-Log-Viewer", "Views and manage system log of your website": "System-Logs Ihrer Website anzeigen und verwalten", "Updater": "Updater", "Updater Booking Core": "Booking Core Updater", "Clear Cache": "<PERSON><PERSON> le<PERSON>n", "Clear Cache for Booking Core": "<PERSON><PERSON> für Booking Core leeren", "No tools available": "Keine Werkzeuge verfügbar", "System Updater": "System-Updater", "Update booking core": "Booking Core aktualisieren", "You are using newest version of Booking Core: :version": "Sie verwenden die neueste Version von Booking Core: :version", "Your license key: :key": "<PERSON><PERSON> Lizenzschlüssel: :key", "Last check for update: :date": "Letzte Update-Prüfung: :date", "Last update success: :date": "Letztes erfolgreiches Update: :date", "Check for update": "<PERSON>ch Updates suchen", "Your current version: :version": "Ihre aktuelle Version: :version", "Latest version available: :version": "Neueste verfügbare Version: :version", "I already back up all files and database": "Ich habe bereits alle Dateien und die Datenbank gesichert", "Update now": "Jetzt aktualisieren", "or": "oder", "change license info": "Lizenzinformationen ändern", "License Key Information": "Lizenzschlüssel-Informationen", "Please enter envato username and license key (purchase code) to get autoupdate": "Bitte geben Sie Envato-Benutzername und Lizenzschlüssel (Kaufcode) ein, um automatische Updates zu erhalten", "Envato username": "Envato-Benutzername", "Your license key (Purchase code)": "Ihr Lizenzschlüssel (Kaufcode)", "How can I get my license key?": "Wie kann ich meinen Lizenzschlüssel erhalten?", "Warning": "<PERSON><PERSON><PERSON>", "Please make sure you back up data before updating": "Bitte stellen Si<PERSON> sicher, dass Sie Daten vor dem Update sichern", "Confirmation": "Bestätigung", "Are you want to update now?. Please make sure you backup all your files and database first": "Möchten Si<PERSON> jetzt aktualisieren? Bitte stellen Si<PERSON> sicher, dass Si<PERSON> zu<PERSON>t alle Dateien und die Datenbank sichern", "Notice": "<PERSON><PERSON><PERSON><PERSON>", "Coupon Management": "Gutschein-Verwaltung", "All Coupons": "Alle Gutscheine", "Edit Coupon: :name": "Gutschein bearbeiten: :name", "Create Coupon": "Gutschein erstellen", "Coupon updated": "Gutschein aktualisiert", "Coupon created": "Gutschein erstellt", "Invalid coupon code!": "Ungültiger Gutscheincode!", "Coupon code is applied successfully!": "Gutscheincode wurde erfolgreich angewendet!", "Coupon code is added already!": "Gutscheincode wurde bereits hinzugefügt!", "This coupon code has expired!": "Dieser Gutscheincode ist abgelaufen!", "The order has not reached the minimum value of :amount to apply the coupon code!": "Die Bestellung hat den Mindestwert von :amount nicht erreicht, um den Gutscheincode anzuwenden!", "This order has exceeded the maximum value of :amount to apply coupon code! ": "Diese Bestellung hat den Höchstwert von :amount überschritten, um den Gutscheincode anzuwenden! ", "Coupon code is not applied to this product!": "Gutscheincode gilt nicht für dieses Produkt!", "You need to log in to use the coupon code!": "<PERSON>e müssen sich anmelden, um den Gutscheincode zu verwenden!", "Coupon code is not applied to your account!": "Gutscheincode gilt nicht für Ihr Konto!", "This coupon code has been used up!": "Dieser Gutscheincode wurde aufgebraucht!", "Add new Coupon": "Neuen Gutschein hinzufügen", "General": "Allgemein", "Coupon Code": "Gutscheincode", "Unique Code": "Eindeutiger Code", "Coupon Name": "Gutschein-Name", "Coupon Amount": "Gutschein-Betrag", "Discount Type": "Rabatt-Typ", "Amount": "Betrag", "End Date": "Enddatum", "2021-12-12 00:00:00": "2021-12-12 00:00:00", "Usage Restriction": "Nutzungsbeschränkung", "Minimum Spend": "Mindestausgabe", "No Minimum": "<PERSON><PERSON>", "The Minimum Spend does not include any Booking fee": "Die Mindestausgabe beinhaltet keine Buchungsgebühr", "Maximum Spend": "Höchstausgabe", "No Maximum": "<PERSON><PERSON>", "The Maximum Spend does not include any Booking fee": "Die Höchstausgabe beinhaltet keine Buchungsgebühr", "Only For Services": "Nur für Services", "-- Select Services --": "-- Services auswählen --", "Only For User": "<PERSON><PERSON> <PERSON>utzer", "Usage Limits": "Nutzungslimits", "Usage Limit per Coupon": "Nutzungslimit pro Gutschein", "Unlimited Usage": "Unbegrenzte Nutzung", "Usage Limit Per User": "Nutzungslimit pro Benutzer", "Add new coupon": "Neuen Gutschein hinzufügen", "Code": "Code", "Is Vendor": "Ist An<PERSON>", "No coupon found": "<PERSON><PERSON>in gefunden", "Coupon code": "Gutscheincode", "[Remove]": "[<PERSON><PERSON><PERSON><PERSON>]", "Course": "<PERSON><PERSON>", "Category": "<PERSON><PERSON><PERSON>", "Category :name": "Kategorie :name", "Category saved": "<PERSON><PERSON><PERSON>", "Courses": "<PERSON><PERSON>", "Course Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Course": "<PERSON><PERSON>", "Add new Course": "Neuen Kurs hinzufügen", "Edit Course": "<PERSON><PERSON> bearbeiten", "Course updated": "<PERSON><PERSON> a<PERSON>", "Course created": "<PERSON><PERSON> er<PERSON>", "Lectures Management": "Vorlesungs-Verwaltung", "Course not found": "Kurs nicht gefunden", "Lecture not found": "Vorlesung nicht gefunden", "Lecture updated": "Vorlesung aktualisiert", "Lecture created": "Vorlesung erstellt", "Delete lecture successfully!": "Vorlesung erfolgreich gelöscht!", "Skill Level": "Fertigkeitslevel", "Level": "Level", "Level :name": "Level :name", "Level saved": "Level gespeichert", "Section not found": "Abschnitt nicht gefunden", "Section updated": "Abschnitt aktualisiert", "Section created": "Abschnitt erstellt", "Delete section successfully!": "Abschnitt erfolgreich gelöscht!", "Style 3": "Stil 3", "Filter by Category": "<PERSON><PERSON> Kategorie filtern", "Courses: List Items": "Kurse: Elemente auflisten", "You are not a student of this course": "<PERSON><PERSON> sind kein Student dies<PERSON>", "Manage Courses": "<PERSON><PERSON> verwalten", "Create Courses": "<PERSON><PERSON> er<PERSON>llen", "Course not found!": "Kurs nicht gefunden!", "Edit Courses": "<PERSON><PERSON> bearbeiten", "Manage Course": "<PERSON><PERSON> ver<PERSON><PERSON>", "Booking Report": "Buchungsbericht", "Add video lecture": "Video-Vorlesung hinzufügen", "Add scorm lecture": "SCORM-Vorlesung hinzufügen", "Add presentation lecture": "Präsentations-Vorlesung hinzufügen", "Add iframe lecture": "iFrame-Vorlesung hinzufügen", "Lecture name is required": "Vorlesungsname ist erforderlich", "Section name is required": "Abschnittsname ist erforderlich", "File is required": "Datei ist erforderlich", "Url is required": "URL ist erforderlich", "Duration is required": "<PERSON>uer ist erforderlich", "Search for Courses": "<PERSON><PERSON>", "\":title\" has been added to your cart.": "\":title\" wurde zu Ihr<PERSON> Ware<PERSON> hinzugefügt.", ":duration Hours": ":duration Stunden", ":duration Minutes": ":duration Minuten", "Course Category": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Course Level": "Kurs-Level", "All Courses": "<PERSON><PERSON>", "Add new course": "Neuen Kurs hinzufügen", "Categories": "<PERSON><PERSON><PERSON>", "Levels": "Level", "Course Settings": "Kurs-Einstellungen", "Course Attributes": "Kurs-Attribute", "Courses Availability Calendar": "Kurs-Verfügbarkeitskalender", "Add new category": "Neue Kategorie hinzufügen", "View": "Anzeigen", "Category Content": "Kategorie-Inhalt", "Category name": "Kategorie-Name", "Parent": "Übergeordnet", "Add Category": "<PERSON><PERSON><PERSON>", "Slug": "Slug", "Course Content": "Kurs-Inhalt", "Short Description": "Kurzbeschreibung", "Duration": "<PERSON><PERSON>", "Ex: 100": "Bsp.: 100", "Hours": "Stunden", "If left blank, the total time of the lectures will automatically be calculated": "<PERSON><PERSON> leer gelassen, wird die Gesamtzeit der Vorlesungen automatisch berechnet", "Preview Video Url": "Vorschau-Video-URL", "Video Url": "Video-URL", "Map Engine": "<PERSON>rten-Engine", "Map Lat": "Karten-Breitengrad", "Map Lng": "Karten-Längengrad", "View Course": "<PERSON><PERSON> anzeigen", "Course Options": "Kurs-Optionen", "Course Featured": "<PERSON><PERSON>", "Teacher Setting": "Lehrer-Einstellung", "All Users": "<PERSON><PERSON>", "Add new user": "Neuen Benutzer hinzufügen", "Search User": "<PERSON><PERSON><PERSON> <PERSON>", "Role": "<PERSON><PERSON>", "Change Password": "Passwort ändern", "All Course": "<PERSON><PERSON>", "Teacher": "<PERSON><PERSON><PERSON>", "Students": "<PERSON><PERSON>", "[Teacher Deleted]": "[<PERSON><PERSON><PERSON>]", "No data found": "<PERSON><PERSON> Daten gefunden", "Add Section": "Abschnitt hinzufügen", "Add lecture": "Vorlesung hinzufügen", "Add video": "Video hinzufügen", "Add presentation": "Präsentation hinzufügen", "Add Iframe": "iFrame hinzufügen", "Add SCORM": "SCORM hinzufügen", "Edit section": "<PERSON><PERSON>chnitt bearbeiten", "Remove section": "Abschnitt entfernen", "Edit Lecture": "Vorlesung bearbeiten", "Lecture name": "Vorlesungsname", "File": "<PERSON><PERSON>", "File URL": "Datei-URL", "Duration (minute)": "<PERSON><PERSON> (Minuten)", "in minutes": "in Minuten", "Preview Url": "Vorschau-URL", "Inactive": "Inaktiv", "Display Order": "Anzeigereihenfolge", "Edit Section": "<PERSON><PERSON>chnitt bearbeiten", "Section name": "Abschnittsname", "Add new level": "Neues Level hinzufügen", "Level Content": "Level-Inhalt", "Level name": "Level-Name", "Add level": "Level hinzufügen", "Sub Title Page": "Untertitel-Seite", "Config review for course": "Bewertung für Kurs konfigurieren", "Enable review system for Course?": "Bewertungssystem für Kurs aktivieren?", "Turn on the mode for reviewing course": "Modus für Kurs-Bewertung aktivieren", "Customer must book a course before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "Config buyer fees for course": "Käufergebühren für Kurs konfigurieren", "Teacher Options": "Lehrer-Optionen", "Teacher config for course": "Lehrer-Konfiguration für Kurs", "Job created by vendor must be approved by admin": "Von <PERSON>ter erstellter Job muss vom Admin genehmigt werden", "Disable course module?": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "Disable course module": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Course name": "<PERSON>rs-Name", "Ratings": "Bewertungen", "Instructors": "Dozenten", "Class icon featured": "Klassen-Icon <PERSON>", "Welcome :name!": "Willkommen :name!", "Earning statistics": "Verdienststatistiken", "Recent Bookings": "Aktuelle Buchungen", "More": "<PERSON><PERSON>", "Item": "Element", "Created At": "Erstellt am", "[Deleted]": "[<PERSON><PERSON><PERSON><PERSON><PERSON>]", "Timeline": "Zeitlinie", "Currency: :currency_main": "Währung: :currency_main", "Yesterday": "Gestern", "Last 7 Days": "Letzte 7 Tage", "Last 30 Days": "Letzte 30 Tage", "This Month": "<PERSON><PERSON>", "Last Month": "Letzter Monat", "This Year": "<PERSON><PERSON>", "This Week": "<PERSON><PERSON>", "DEMO MODE: Disable update": "DEMO-MODUS: Update deaktiviert", "Email Settings": "E-Mail-Einstellungen", "Config Email": "E-Mail konfigurieren", "Change your config email site": "Ihre E-Mail-Website-Konfiguration ändern", "Email Driver": "E-Mail-Treiber", "Email Host": "E-Mail-Host", "Email Port": "E-Mail-Port", "Email Encryption": "E-Mail-Verschlüsselung", "Email Username": "E-Mail-Benutzername", "Email Password": "E-Mail-Passwort", "Mailgun Domain": "Mailgun-Domain", "Mailgun Secret": "Mailgun-Secret", "Mailgun Endpoint": "Mailgun-Endpunkt", "Postmark Token": "Postmark-Token", "Ses Key": "SES-Schlüssel", "Ses Secret": "SES-Secret", "Ses Region": "SES-Region", "Sparkpost Secret": "Sparkpost-Secret", "Email From Config": "E-Mail-Von-Konfiguration", "How your customer can contact to you": "Wie Ihre Kunden Sie kontaktieren können", "Admin Email": "Admin-E-Mail", "You will get all notifications from this email": "Sie erhalten alle Benachrichtigungen von dieser E-Mail", "Email Form Name": "E-Mail-Formular-Name", "Email Form Address": "E-Mail-Formular-<PERSON><PERSON><PERSON>", "Email Testing": "E-Mail-Test", "Send Email Test": "E-Mail-Test senden", "Email Header & Footer": "E-Mail-Kopf- und Fußzeile", "Change booking email header and footer": "Buchungs-E-Mail-Kopf- und Fußzeile ändern", "Header": "Kopfzeile", "Event": "Event", "Events": "Events", "Event Management": "Event-<PERSON>erwaltung", "Recovery Event Management": "Event-Verwaltung wiederherstellen", "Add Event": "Event hinzufügen", "Add new Event": "Neues Event hinzufügen", "Edit Event": "Event bearbeiten", "Event updated": "Event aktualisiert", "Event created": "Event erstellt", "Service Event": "Service Event", "Event: Form Search": "Event: Suchformular", "Event: List Items": "Event: Elemente auflisten", "Events Availability": "Event-Verfügbarkeit", "Event not found": "Event nicht gefunden", ":count events found": ":count Events gefunden", ":count event found": ":count Event gefunden", "Showing :from - :to of :total Events": "Zeige :from - :to von :total Events", "Manage Events": "<PERSON> verwalten", "Recovery Events": "Events wiederherstellen", "Restore event success!": "Event erfolgreich wiederhergestellt!", "Create Events": "Events erstellen", "Event not found!": "Event nicht gefunden!", "Edit Events": "Events bearbeiten", "Delete event success!": "Event erfolgreich gelöscht!", "Search for Events": "Nach Events suchen", "Start date is not a valid date": "Startdatum ist kein gültiges Datum", "Please select ticket!": "Bitte Ticket auswählen!", "There are :numberTicket :titleTicket available for your selected date": "Es sind :numberTicket :titleTicket für Ihr ausgewähltes Datum verfügbar", "Please select start time!": "Bitte Startzeit auswählen!", ":slot not available for your selected ": ":slot nicht verfügbar für Ihre Auswahl ", "ticket": "Ticket", ":number Events": ":number Events", ":number Event": ":number Event", "All Events": "Alle Events", "Manage Event": "Event verwalten", "Event Settings": "Event-Einstellungen", "Event Attributes": "Event-Attribute", "Events Availability Calendar": "Event-Verfügbarkeitskalender", "Showing :from - :to of :total events": "Zeige :from - :to von :total Events", "No events found": "Keine Events gefunden", "Add new event": "Neues Event hinzufügen", "View Event": "Event anzeigen", "Event Featured": "Event hervorgehoben", "Event Content": "Event-Inhalt", "Start Time": "Startzeit", "Ex: 15:00": "Bsp.: 15:00", "Input time format, ex: 15:00": "Zeitformat eingeben, Bsp.: 15:00", "End Time": "Endzeit", "Ex: 21:00": "Bsp.: 21:00", "Input time format, ex: 21:00": "Zeitformat eingeben, Bsp.: 21:00", "Duration (hour)": "<PERSON>uer (Stunden)", "Duration Unit": "Dauer-Einheit", "Hour": "Stunde", "Minute": "Minute", "Event Price": "Event-Preis", "Event Sale Price": "Event-Verkaufspreis", "Tickets": "Tickets", "Price - Number": "Preis <PERSON> <PERSON><PERSON><PERSON>", "ticket_vip_1": "ticket_vip_1", "Price Ticket": "Ticket-Preis", "Number Ticket": "Ticket-<PERSON><PERSON><PERSON>", "Per hour": "Pro Stunde", "Price per ticket": "Preis pro Ticket", "No event found": "Kein Event gefunden", "Config review for event": "Bewertung für Event konfigurieren", "Enable review system for Event?": "Bewertungssystem für Event aktivieren?", "Turn on the mode for reviewing event": "Modus für Event-Bewertung aktivieren", "Customer must book a event before writing a review?": "Kunde muss <PERSON> buchen, bevor er eine Bewertung schreibt?", "Booking Options": "Buchungsoptionen", "Config Booking for event": "Buchung für Event konfigurieren", "Booking Type": "Buchungstyp", "Ticket": "Ticket", "Time slot": "Zeitfenster", "Booking Buyer Fees": "Buchungs-Käufergebühren", "Vendor config for event": "Anbieter-Konfiguration für Event", "Event created by vendor must be approved by admin": "<PERSON> erstelltes Event muss vom Admin genehmigt werden", "Disable event module?": "Event-<PERSON><PERSON><PERSON>?", "Disable event module": "Event-<PERSON><PERSON><PERSON>", "Event information": "Event-<PERSON>en", "Event name": "Event-Name", "Duration:": "Dauer:", ":count hour": ":count <PERSON><PERSON><PERSON>", ":count hours": ":count <PERSON><PERSON><PERSON>", "Start Time:": "Startzeit:", "Airline": "Fluggesellschaft", "Airline Management": "Fluggesellschafts-Verwaltung", "Edit airline": "Fluggesellschaft bearbeiten", "Airline saved": "Fluggesellschaft gespeichert", "Airport": "Flughafen", "Airport Management": "Flughafen-Verwaltung", "Edit airport": "Flughafen bearbeiten", "Airport saved": "Flughafen gespeichert", "Import Queued": "Import in Warteschlange", "Flight": "Flug", "Flights": "Flüge", "Flight Management": "Flug-Verwaltung", "Recovery Flight Management": "Flug-Verwaltung wiederherstellen", "Add Flight": "Flug hinzufügen", "Add new Flight": "Neuen Flug hinzufügen", "Edit Flight": "Flug bearbeiten", "Edit: #:name": "Bearbeiten: #:name", "Flight updated": "Flug aktualisiert", "Flight created": "Flug erstellt", "Flight: :name :code #:id": "Flug: :name :code #:id", "Flight seat": "Flugsitz", "Flight seat Management": "Flugsitz-Verwaltung", "Edit flight_seat": "Flugsitz bearbeiten", "Flight seat saved": "Flugsitz gespeichert", "Seat Type": "Sitztyp", "Seat Type Management": "Sitztyp-Verwaltung", "Seat type": "Sitztyp", "Edit seat type": "Sitztyp bearbeiten", "Seat type saved": "Sitztyp gespeichert", "Flight: Form Search": "Flug: Such<PERSON>ular", "Flight Blocks": "Flug-B<PERSON><PERSON>cke", ":count flights found": ":count <PERSON><PERSON><PERSON><PERSON> gefunden", ":count flight found": ":count Flug gefunden", "Showing :from - :to of :total Flights": "Zeige :from - :to von :total Flügen", "Manage Flights": "Flüge verwalten", "Recovery Flights": "Flüge wiederherstellen", "Restore flight success!": "Flug erfolgreich wiederhergestellt!", "Create Flights": "Flüge erstellen", "Flight not found!": "Flug nicht gefunden!", "Edit Flights": "Flüge bearbeiten", "Delete flight success!": "Flug erfolgreich gelöscht!", "Flight clone was successful": "Flug-Klonen war erfolgreich", "Flight: :name": "Flug: :name", "All Flight seats": "Alle Flugsitze", "Create Flight seat": "Flugsitz erstellen", "Edit  :name": "Bearbeiten :name", "Flight seat updated": "Flugsitz aktualisiert", "Flight seat created": "Flugsitz erstellt", "Delete room success!": "Raum erfolgreich gelöscht!", "Search for Flights": "<PERSON><PERSON> Flügen suchen", ":number Flights": ":number Flüge", ":number Flight": ":number Flug", "All Flights": "Alle Flüge", "Manage Flight": "Flug verwalten", "Add Flights": "Flüge hinzufügen", "Flight Settings": "Flug-Einstellungen", "Airline Content": "Fluggesellschafts-Inhalt", "Airline: :name": "Fluggesellschaft: :name", "Add Airline": "Fluggesellschaft hinzufügen", "Airport Content": "Flughafen-Inhalt", "IATA Code": "IATA-Code", "Airport: :name": "Flughafen: :name", "Import from IATA": "Von IATA importieren", "Add Airport": "Flughafen hinzufügen", " Mark as Publish ": " Als veröffentlicht markieren ", " Mark as Draft ": " Als Entwurf markieren ", "Found :count airport(s)": ":count Flughafen/Flughäfen gefunden", "Flight Attributes": "Flug-Attribute", "Add new flight": "Neuen Flug hinzufügen", " Flight Ticket type": " Flugticket-Typ", "Add new seat type": "Neuen Sitztyp hinzufügen", "Flight Content": "Flug-Inhalt", "-- Select Airport from --": "-- Abflughafen auswählen --", "To": "Nach", "-- Select Airport to --": "-- Zielflughafen auswählen --", "Airline and time": "Fluggesellschaft und Zeit", "-- Select Airline --": "-- Fluggesellschaft auswählen --", "Departure time": "Abflugzeit", "Arrival time": "Ankunftszeit", "hours": "Stunden", "Search by code": "Nach Code suchen", "All Flight": "Alle Flüge", "-- Select seat type --": "-- Sitztyp auswählen --", "Max passengers": "<PERSON><PERSON>", "Person type": "Personentyp", "Adult": "Erwachsener", "Child": "Kind", "Baggage Check in": "Aufgabegepäck", "Baggage Cabin": "Handgepäck", "Add Flight Seat": "Flugsitz hinzufügen", "All Flight seat": "Alle Flugsitze", "Airport From": "Abflughafen", "Airport To": "Zielflughafen", "Flight ticket": "Flugticket", "No flight found": "<PERSON><PERSON>lug gefunden", "Seat type Content": "Sitztyp-Inhalt", "Seat Type: :name": "Sitztyp: :name", "Add Seat Type": "Sitztyp hinzufügen", "From where": "<PERSON> wo", "To where": "W<PERSON>in", "Config review for flight": "Bewertung für Flug konfigurieren", "Enable review system for Flight?": "Bewertungssystem für Flug aktivieren?", "Turn on the mode for reviewing flight": "Modus für Flug-Bewertung aktivieren", "Customer must book a flight before writing a review?": "Kunde muss Flug buchen, bevor er eine Bewertung schreibt?", "Config Booking for flight": "Buchung für Flug konfigurieren", "Flight by day": "Flug am Tag", "Flight by night": "Flug in der Nacht", "Vendor config for flight": "Anbieter-Konfiguration für Flug", "Flight created by vendor must be approved by admin": "<PERSON> erstellter Flug muss vom Admin genehmigt werden", "Disable flight module?": "Flug-<PERSON><PERSON><PERSON>?", "Disable flight module": "Flug-<PERSON><PERSON><PERSON>", "Flight information": "Flug-Informationen", "Flight name": "Flug-Name", "Nights:": "Nächte:", "Rental price": "Mietpreis", "Hotel": "Hotel", "Hotels": "Hotels", "Hotel Management": "Hotel-Verwaltung", "Add Hotel": "Hotel hinzufügen", "Add new Hotel": "Neues Hotel hinzufügen", "Recovery Hotel Management": "Hotel-Verwaltung wiederherstellen", "Edit Hotel": "Hotel bearbeiten", "Hotel updated": "Hotel aktualisiert", "Hotel created": "Hotel erstellt", "Room Attributes": "Zimmer-Attribute", "Hotel: :name": "Hotel: :name", "Room Management": "Zimmer-Verwaltung", "All Rooms": "<PERSON><PERSON>", "Edit room: :name": "Zimmer bearbeiten: :name", "Room updated": "<PERSON><PERSON> aktualisiert", "Room created": "<PERSON><PERSON> er<PERSON>", "Hotel: Form Search": "Hotel: Suchformular", "Service Hotel": "Service Hotel", "Hotel: List Items": "Hotel: Elemente auflisten", "Room Availability": "Zimmer-Verfügbarkeit", "Hotel not found": "Hotel nicht gefunden", "room not found": "<PERSON>immer nicht gefunden", "Room not found": "<PERSON>immer nicht gefunden", ":count hotels found": ":count Hotels gefunden", ":count hotel found": ":count Hotel gefunden", "Showing :from - :to of :total Hotels": "Zeige :from - :to von :total Hotels", "Dates are not valid": "<PERSON>rmine sind nicht gültig", "Maximum day for booking is 30": "Maximale Buchungsdauer beträgt 30 Tage", "Manage Hotels": "Hotels verwalten", "Recovery Hotels": "Hotels wiederherstellen", "Create Hotels": "Hotels erstellen", "Space not found!": "Raum nicht gefunden!", "Edit Hotels": "Hotels bearbeiten", "Delete hotel success!": "Hotel erfolgreich gelöscht!", "Restore hotel success!": "Hotel erfolgreich wiederhergestellt!", "Create Room": "<PERSON><PERSON>", "Search for Spaces": "<PERSON><PERSON> R<PERSON> suchen", "Please select at lease one room": "Bitte wählen Sie mindestens ein Zimmer", "There is no room available at your selected dates": "Es ist kein Z<PERSON> an Ihren ausgewählten Terminen verfügbar", "Your selected room is not available. Please search again": "Ihr ausgewähltes Zimmer ist nicht verfügbar. Bitte suchen Si<PERSON> erneut", "The :name need to select at least :number days": "Das :name muss mindestens :number Tage auswählen", "Sorry, the current rooms are not enough for adults": "Entschuldigung, die aktuellen Zimmer reichen nicht für Erwachsene", "Sorry, the current rooms are not enough for children": "Entschuldigung, die aktuellen Zimmer reichen nicht für Kinder", "Please select check-in and check-out date": "Bitte Check-in- und Check-out-<PERSON><PERSON> auswählen", "rooms": "<PERSON><PERSON>", "room": "<PERSON><PERSON>", ":number Hotels": ":number Hotels", ":number Hotel": ":number Hotel", ":count nights": ":count <PERSON><PERSON><PERSON><PERSON>", ":count night": ":count <PERSON><PERSON>", "Hotel Star": "Hotel-Sterne", "Rooms": "<PERSON><PERSON>", "Hotel Room": "Hotelzimmer", "All Hotels": "Alle Hotels", "Manage Hotel": "Hotel verwalten", "Hotel Settings": "Hotel-Einstellungen", "Hotel Attributes": "Hotel-Attribute", "Add new hotel": "Neues Hotel hinzufügen", "Manage Rooms": "<PERSON><PERSON> ver<PERSON><PERSON>", "View Hotel": "Hotel anzeigen", "Hotel Featured": "Hotel hervorgehoben", "Hotel Content": "Hotel-Inhalt", "Name of the hotel": "Name des Hotels", "Hotel Policy": "Hotel-Richtlinie", "Hotel rating standard": "Hotel-Bewertungsstandard", "Eg: 5": "Z.B.: 5", "Policy": "Rich<PERSON><PERSON><PERSON>", "Eg: What kind of foowear is most suitable ?": "Z.B.: Welche Art von Schuhwerk ist am besten geeignet?", "Check in/out time": "Check-in/Check-out-<PERSON><PERSON>", "Allowed full day booking": "Ganztägige Buchung erlaubt", "Enable full day booking": "Ganztägige Buchung aktivieren", "You can book room with full day": "<PERSON>e können Z<PERSON> ganztägig buchen", "Eg: booking from 22-23, then all days 22 and 23 are full, other people cannot book": "Z.B.: <PERSON>uchung vom 22.-23., dann sind alle Tage 22 und 23 belegt, andere können nicht buchen", "Time for check in": "Zeit für Check-in", "Eg: 12:00AM": "Z.B.: 12:00", "Time for check out": "Zeit für Check-out", "Eg: 11:00AM": "Z.B.: 11:00", "Hotel Price": "Hotel-Preis", "Hotel Sale Price": "Hotel-Verkaufspreis", "Surroundings": "Umgebung", "Distance": "Entfernung", "Sunny Beach": "Sonnenstrand", "m": "m", "km": "km", "Edit hotel": "Hotel bearbeiten", "Manage Rooms Availability": "Zimmer-Verfügbarkeit verwalten", "No hotel found": "Kein Hotel gefunden", "Room Availability Calendar": "Zimmer-Verfügbarkeitskalender", "Showing :from - :to of :total rooms": "Zeige :from - :to von :total Zimmern", "No rooms found": "<PERSON><PERSON> gefunden", "Number of room": "<PERSON><PERSON><PERSON>", "Add new Hotel Room": "Neues Hotelzimmer hinzufügen", "Room information": "Zimmer-Informationen", "Room Name": "<PERSON><PERSON>-Name", "Room name": "<PERSON><PERSON>-Name", "Room Description": "Zimmer-Beschreibung", "Number of beds": "<PERSON><PERSON><PERSON>", "Room Size": "Zimmergröße", "Room size": "Zimmergröße", "Max Adults": "<PERSON><PERSON>", "Max Children": "<PERSON><PERSON>", "Back to hotel": "Zurück zum Hotel", "Add Room": "<PERSON><PERSON>", "No room found": "<PERSON><PERSON> gefunden", "Guests": "<PERSON><PERSON><PERSON>", "Layout Item Hotel In Page Search": "Layout-Element Hotel in Seitensuche", "List Item": "Listenelement", "Grid Item": "Rasterelement", "Which attribute show in listing page?": "Welches Attribut in der Auflistungsseite anzeigen?", "Config review for hotel": "Bewertung für Hotel konfigurieren", "Enable review system for Hotel?": "Bewertungssystem für Hotel aktivieren?", "Turn on the mode for reviewing hotel": "Modus für Hotel-Bewertung aktivieren", "Customer must book a hotel before writing a review?": "Kunde muss Hotel buchen, bevor er eine Bewertung schreibt?", "Config buyer fees for hotel": "Käufergebühren für Hotel konfigurieren", "Vendor config for hotel": "Anbieter-Konfiguration für Hotel", "Hotel created by vendor must be approved by admin": "Von Anbieter erstelltes Hotel muss vom Admin genehmigt werden", "Disable hotel module?": "Hotel-<PERSON><PERSON><PERSON>?", "Disable hotel module": "Hotel-<PERSON><PERSON><PERSON>", "Hotel information": "Hotel-Informationen", "Hotel name": "Hotel-Name", "Check in": "Check-in", "Check out:": "Check-out:", "Language created": "Sprache erstellt", "Language Management": "Sprachen-Verwaltung", "Language updated": "Sprache aktualisiert", "Translate for: :name": "Übersetzen für: :name", "Translation saved": "Übersetzung gespeichert", "Folder: resources/lang is not write-able. Please contact your hosting provider": "Ordner: resources/lang ist nicht beschreibbar. Bitte kontaktieren Sie Ihren Hosting-Anbieter", "File: :file_name is not write-able. Please contact your hosting provider": "Datei: :file_name ist nicht beschreibbar. Bitte kontaktieren Sie Ihren Hosting-Anbieter", "Re-build language file for: :name success": "Sprachdatei für :name erfolgreich neu erstellt", "Default language source does not exists": "Standard-<PERSON><PERSON><PERSON><PERSON><PERSON> existiert nicht", "Default language source empty": "Standard-<PERSON><PERSON><PERSON><PERSON><PERSON> ist leer", "Default language source do not have any strings": "Standard-S<PERSON>ch<PERSON>le hat keine Zeichenketten", "Loaded :count strings": ":count <PERSON><PERSON><PERSON><PERSON><PERSON>", "Generate Default JSON Language": "Standard-JSON-Sprache generieren", "File language source does not exists": "Datei-<PERSON><PERSON><PERSON><PERSON><PERSON> exist<PERSON>t nicht", "File language source empty": "Date<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> ist leer", "File language source do not have any strings": "Datei-<PERSON><PERSON><PERSON><PERSON><PERSON> hat keine Zeichenketten", "Load language from json success": "Sprache aus JSON erfolgreich geladen", "Add new location": "Neuen Standort hinzufügen", "Language Content": "Sprach-Inhalt", "Locale": "Gebietsschema", "Flag Icon": "Flaggen-Icon", "Eg: gb": "Z.B.: de", "Please input flag code": "Bitte Flaggen-Code eingeben", "Display Name": "Anzeigename", "Please input language name": "Bitte Sprachname eingeben", "Add Language": "Sprache hinzufügen", "All Languages": "Alle Sprachen", "Translate Manager for: :name": "Übersetzungsmanager für: :name", "All text": "Alle Texte", "Not translated": "Nicht übersetzt", "Translated": "Übersetzt", "Search By": "<PERSON><PERSON> nach", "Original Text": "Originaltext", "Translated Text": "Übersetzter Text", "Search by key ...": "<PERSON><PERSON> suchen ...", "Filter": "Filter", "Found :total texts": ":total Texte gefunden", "Translate": "Übersetzen", "Origin": "Ursprung", "Find Translations": "Übersetzungen finden", "After translation. You must re-build language file to apply the change": "Nach der Übersetzung müssen Sie die Sprachdatei neu erstellen, um die Änderung anzuwenden", "Last build at": "Zuletzt erstellt am", "Build": "<PERSON><PERSON><PERSON><PERSON>", "Booking Core by": "Booking Core von", "https://www.bookingcore.co": "https://www.bookingcore.co", "BookingCore Team": "BookingCore Team", "About Us": "Über uns", "https://m.me/bookingcore": "https://m.me/bookingcore", "Contact Us": "Kontaktieren Sie uns", "Please check the form below for errors": "Bitte überprüfen Sie das untenstehende Formular auf Fehler", "Do you want to restore?": "Möchten Sie wiederherstellen?", "Confirm": "Bestätigen", "Custom Range": "Benutzerdefinierter Bereich", "W": "W", "Su": "So", "Mo": "Mo", "Tu": "Di", "We": "<PERSON>", "Th": "Do", "Fr": "Fr", "Sa": "Sa", "January": "<PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON><PERSON>", "April": "April", "May": "<PERSON>", "June": "<PERSON><PERSON>", "July": "<PERSON><PERSON>", "August": "August", "September": "September", "October": "Oktober", "November": "November", "December": "Dezember", "Image Editor": "Bildbearbeitung", "Toggle fullscreen": "Vollbild umschalten", "Close window": "Fenster schließen", "Save": "Speichern", "Save As New Image": "Als neues Bild speichern", "Go Back": "Zurück gehen", "Adjust": "<PERSON><PERSON><PERSON>", "Effects": "Effekte", "Filters": "Filter", "Orientation": "Ausrichtung", "Crop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Resize": "Größe ändern", "Watermark": "Wasserzeichen", "Focus point": "Fokuspunkt", "Shapes": "Formen", "Brightness": "Helligkeit", "Contrast": "<PERSON><PERSON><PERSON><PERSON>", "Exposure": "Belichtung", "Saturation": "Sättigung", "Rotate Left": "Nach links drehen", "Rotate Right": "Nach rechts drehen", "Flip Horizontally": "Horizontal spiegeln", "Flip Vertically": "Vertikal spiegeln", "Would you like to reduce resolution before editing the image?": "Möchten Sie die Auflösung vor der Bildbearbeitung reduzieren?", "Keep original resolution": "Ursprüngliche Auflösung beibehalten", "Resize & Continue": "Größe ändern & Fortfahren", "Reset": "Z<PERSON>ücksetzen", "Undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Processing...": "Verarbeitung...", "The resolution of the image is too big for the web. It can cause problems with Image Editor performance.": "Die Auflösung des Bildes ist zu groß für das Web. Es kann Probleme mit der Bildbearbeitungsleistung verursachen.", "x": "x", "y": "y", "width": "Breite", "height": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "original": "original", "square": "quadratisch", "Opacity": "Deckkraft", "Apply watermark": "Wasserzeichen anwenden", "Upload": "Hochladen", "Home": "Startseite", "Upgrade": "Upgrade", "Mark all as read": "Alle als gelesen markieren", "Notifications": "Benachrichtigungen", "View More": "<PERSON><PERSON> anzeigen", "Edit Profile": "<PERSON><PERSON>", "Vendor Dashboard": "Anbieter-Dashboard", "Logout": "Abmelden", "Email address": "E-Mail-Adresse", "Remember me": "Ang<PERSON><PERSON><PERSON> bleiben", "Forgot Password?": "Passwort vergessen?", "or continue with": "oder fortfahren mit", "Do not have an account?": "<PERSON>ben <PERSON> kein Konto?", "Reset Password": "Passwort zurücksetzen", "E-Mail Address": "E-Mail-Adresse", "Send Password Reset Link": "Passwort-Reset-Link senden", "Confirm Password": "Passwort bestätigen", "I have read and accept the <a href=':link' target='_blank'>Terms and Privacy Policy</a>": "Ich habe die <a href=':link' target='_blank'>Geschäftsbedingungen und Datenschutzrichtlinie</a> gelesen und akzeptiert", " Already have an account?": " Haben <PERSON> bereits ein Konto?", "Log In": "Anmelden", "Register": "Registrieren", "Verify Your Email Address": "Verifizieren Sie Ihre E-Mail-Adresse", "A fresh verification link has been sent to your email address.": "Ein neuer Verifizierungslink wurde an Ihre E-Mail-Adresse gesendet.", "Before proceeding, please check your email for a verification link.": "<PERSON><PERSON> fort<PERSON>hren, überprüfen Sie bitte Ihre E-Mail auf einen Verifizierungslink.", "If you did not receive the email": "Wenn Sie die E-Mail nicht erhalten haben", "click here to request another": "klicken <PERSON> hier, um eine weitere anzu<PERSON>ern", "We use cookies!": "Wir verwenden Cookies!", "Reject all": "<PERSON>e <PERSON>en", "Get Updates & More": "Updates & Mehr erhalten", "Thoughtful thoughts to your inbox": "Durchdachte Gedanken in Ihren Posteingang", "Your Email": "Ihre E-Mail", "Subscribe": "Abonnieren", "Hi, :Name": "Hallo, :Name", "My profile": "<PERSON><PERSON>", "Messages": "Nachrichten", "Change password": "Passwort ändern", "Admin Dashboard": "Admin-Dashboard", "Hi, :name": "Hallo, :name", "Credit: :amount": "Guthaben: :amount", "My plan": "Mein <PERSON>", "Support Center": "Support-Center", "Location updated": "Standort aktualisiert", "Location created": "Standort erstellt", "Service Type": "Service-Typ", "Style 4": "Stil 4", "List Location by IDs": "Standorte nach IDs auflisten", "Link to location detail page?": "Link zur Standort-Detailseite?", "List Locations": "<PERSON><PERSON><PERSON> auflisten", "Location Category": "Standort-<PERSON><PERSON><PERSON>", "All Location": "<PERSON><PERSON> Standorte", "All Category": "Alle Kategorien", "Icon class": "Icon-Klasse", "Location Categories": "Standort-<PERSON><PERSON><PERSON>", "Location Content": "Standort-Inhalt", "Trip Ideas": "Reiseideen", "Title/Link": "Titel/Link", "Title:": "Titel:", "Link:": "Link:", "Location name": "Standort-Name", "Add Location": "Standort hinzufügen", "Location Map": "Standort-Karte", "Click onto map to place Location address": "<PERSON><PERSON> klicken, um Standort-Adresse zu platzieren", "Media Management": "", "Please log in": "", "Can not remove!": "", "Please select file": "", "You don't have permission delete the file!": "", "Delete the file success!": "", "File not found!": "", "403": "", "You are not allowed to edit this folder": "", "Folder name exists, please select new one": "", "You are not allowed to delete this folder": "", "Folder deleted": "", "Can not upload the file": "", "File type are not allowed": "", "Maximum upload file size is :max_size B": "", "Can not get image dimensions": "", "Maximum width allowed is: :number": "", "Maximum height allowed is: :number": "", "Upload image": "", "Select images": "", "Can not edit non-local images": "", "Update Successful": "", "Media": "", "File type invalid": "", "Can not get image size": "", "Media Settings": "", "Folder not found. Please try again": "", "Can not upload file": "", "Search file name....": "", "files": "", "Add Folder": "", "No file found": "", "Previous": "", "Next": "", "Delete file": "", "file selected": "", "unselect": "", "Use file": "", "Cloud Storage Configs": "", "Select Cloud Driver": "", "-- Local Storage --": "", "AWS S3": "", "Google Cloud Storage": "", "Amazon S3": "", "Key": "", "Secret access key": "", "Default region": "", "Bucket": "", "Project ID": "", "Service Account Key File Name": "", "View file": "", "Folder": "", "Delete this folder": "", "Category updated": "", "Category created": "", "Please select an Action!": "", "News Management": "", "Add News": "", "News updated": "", "News created": "", "News does not exists": "", "Language does not exists": "", "Tag": "", "Tag updated": "", "Tag Created": "", "News: List Items": "", "Search results : \":s\"": "", "News Tag": "", "New Tag": "", "All News": "", "Tags": "", "Manage News": "", "News Settings": "", "Permalink:": "", "News Categories": "", "Search Category": "", "Edit post: ": "", "Add new Post": "", "View Post": "", "News content": "", "Enter tag": "", "All news": "", " Move to Pending ": "", "--All Category --": "", "Search News": "", "Page List": "", "Config page list news of your website": "", "Posts Per Page": "", "Sidebar Options": "", "Config sidebar for news": "", "Title: About Us": "", "Search Form": "", "Recent News": "", "Featured Listings": "", "Content Text": "", "Vendor News": "", "Config for vendor": "", "Admin need approve news to be publish": "", "Tag Content": "", "Tag name": "", "Tag Slug": "", "News Tags": "", "Add Tag": "", "Search keyword ...": "", "Search Tag": "", "Page Management": "", "Pages": "", "Add Page": "", "Edit Page": "", "Page updated": "", "Page created": "", "Header Style": "", "Transparent": "", "Add new page": "", "Permalink: ": "", "Template Builder": "", "View page": "", "Page Content": "", "All Page": "", "Search Page": "", "Popups": "", "Popup Management": "", "Recovery Popup Management": "", "Add Popup": "", "Add new Popup": "", "Edit Popup": "", "Popup updated": "", "Popup created": "", "Popup": "", "Add new popup": "", "Preview": "", "All Popups": "", "No popup found": "", "Show on": "", "Include URLs": "", "Wildcard allowed. Eg: */checkout/* ": "", "Exclude URLs": "", "Popup name": "", "Schedule": "", "Show every": "", "Day": "", "Month": "", "Year": "", "Property": "", "Properties": "", "Property Management": "", "Add Property": "", "Add new Property": "", "Edit Property": "", "Property updated": "", "Property created": "", "Contact property": "", "Title Link More": "", "Link More": "", "Background Color - get code in <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>": "", "Style 1 : Background Color Only": "", "Style 2 : Background Image": "", "Style 3 : Background Image + Color": "", "Call To Action": "", "Property: Form Search": "", "Layout": "", "Carousel Layout": "", "View All Option": "", "View All With 3 Items Grid": "", "Hide button scroll down?": "", "Property: List Items": "", "Filter by Role": "", "User: List Users": "", "Select term property": "", "Property: Term Featured Box": "", "Sub title": "", "Number star": "", "Position": "", "List Testimonial": "", "Properties Availability": "", "Property not found": "", "Manage Properties": "", "Create Properties": "", "Property not found!": "", "Edit Properties": "", "Delete property success!": "", "Manage Property": "", "Property clone was successful": "", "Showing :from - :to of :total properties": "", "Search for Properties": "", "Maximum guests is :count": "", "This property is not available at selected dates": "", ":number Properties": "", ":number Property": "", "From ": "", "For Buy": "", "For Rent": "", "Property Category": "", "All Properties": "", "Property Settings": "", "Property Attributes": "", "Properties Availability Calendar": "", "No properties found": "", "Property Categories": "", "Name of property": "", "Name of vendor": "", "Add new property": "", "View Property": "", "Property type": "", "For buy": "", "For rent": "", "Property Featured": "", "Sold": "", "Sold Out": "", "No property found": "", "Property Content": "", "Video Background": "", "No. Bed": "", "No. Bathroom": "", "Square": "", "Example: 100": "", "Garages": "", "Year built": "", "Example: 2020": "", "Area": "", "Additional details": "", "Deposit": "", "Pool size": "", "Additional zoom": "", "Remodal year": "", "Amenities": "", "Equipment": "", "Property Price": "", "Property Sale Price": "", "Max Guests": "", "Discount by number of people": "", "No of people": "", "Discount": "", "Percent (%)": "", "Property Type": "", "Bathrooms": "", "Bedrooms": "", "Year Built": "", "Keyword": "", "-- Select Size --": "", "Page list properties layout": "", "Display Type": "", "Add prefix Price in Property listing?": "", "Open gallery when clicking Featured image on the Listing page?": "", "Layout Map Position": "", "Layout Map Size": "", "Page Detail": "", "Config page detail property of your website": "", "Page single property layout": "", "Config review for property": "", "Enable review system for Property?": "", "Turn on the mode for reviewing property": "", "Agent config for property": "", "Property created by vendor must be approved by admin": "", "Property information": "", "Property name": "", "All Bookings": "", "No items selected": "", "Please select action": "", "Search results: \":s\"": "", "Enquiry Management": "", "Enquiry :name": "", "All Replies": "", "Replies": "", "Reply added": "", "Reports :count": "", "Enquiry Reports": "", "Booking Reports": "", "Booking Statistic": "", "Credit Purchase Report :count": "", "-- Bulk Actions --": "", "Mark as: :name": "", "DELETE booking": "", "Search by name or ID": "", "Service": "", "Payment Information": "", "Commission": "", "by": "", "Name:": "", "Email:": "", "Phone:": "", "Address:": "", "Custom Requirement:": "", "Detail": "", "Set Paid": "", "Email Preview": "", "Booking ID: #": "", "All Enquiries": "", "DELETE Enquiry": "", "Search by email": "", "Enquiries": "", "Notes:": "", "Reply": "", "All Reply": "", "Add Reply": "", "Client Message:": "", "Content:": "", "Reply Content": "", "Add New": "", "Recent updates": "", "Bookings Statistic": "", "Filter:": "", "-- User Type --": "", "Customer User": "", "Vendor User": "", "Detail statistics": "", "Review": "", "Review not enable": "", "You need to make a booking or the Orders must be confirmed before writing a review": "", "You cannot review your service": "", "Review Title is required field": "", "Review Content is required field": "", "Review Content has at least 10 character": "", "Review success!": "", "Review success! Please wait for admin approved!": "", "Review error!": "", "Excellent": "", "Very Good": "", "Average": "", "Poor": "", "Terrible": "", "Review Advanced Settings": "", "All Reviews": "", " Approved ": "", " Pending ": "", " Spam ": "", " Move to Trash ": "", "-- Customer --": "", "Search by title": "", "Approved": "", "Spam": "", "Trash": "", "Review Content": "", "In Response To": "", "Submitted On": "", "More info": "", "View :name": "", "Allow customer upload picture to review": "", "Based on": "", ":number reviews": "", ":number review": "", "Showing :from - :to of :total total": "", "No Review": "", "Write a review": "", "Review title is required": "", "Review content": "", "Review content has at least 10 character": "", "Review rate": "", "Add photo": "", "Leave a Review": "", "You must <a href='#login' data-toggle='modal' data-target='#login'>log in</a> to write review": "", "Sms Settings": "", "Social": "", "Forum": "", "forum saved": "", "News Feed": "", "Forums": "", "Add new post": "", "How are you feeling today?": "", "Share": "", "Like": "", "Comments": "", "Space": "", "Spaces": "", "Space Management": "", "Recovery Space Management": "", "Add Space": "", "Add new Space": "", "Edit Space": "", "Space updated": "", "Space created": "", "Space: Form Search": "", "Service Space": "", "Space: List Items": "", "Space: Term Featured Box": "", "Select term space": "", "Spaces Availability": "", "Space not found": "", "Manage Spaces": "", "Recovery Spaces": "", "Restore space success!": "", "Create Spaces": "", "Edit Spaces": "", "Delete space success!": "", "Space clone was successful": "", ":count spaces found": "", ":count space found": "", "Showing :from - :to of :total Spaces": "", "This space is not available at selected dates": "", "You must to book a minimum of :number nights": "", ":number Spaces": "", ":number Space": "", "All Spaces": "", "Manage Space": "", "Space Settings": "", "Space Attributes": "", "Spaces Availability Calendar": "", "Showing :from - :to of :total spaces": "", "No spaces found": "", "Add new space": "", "View Space": "", "Space Featured": "", "No space found": "", "Config review for space": "", "Enable review system for Space?": "", "Turn on the mode for reviewing space": "", "Customer must book a space before writing a review?": "", "Config Booking for space": "", "Space by day": "", "Space by night": "", "Vendor config for space": "", "Space created by vendor must be approved by admin": "", "Disable space module?": "", "Disable space module": "", "Space Content": "", "Space Price": "", "Space Sale Price": "", "Space information": "", "Space name": "", "Support Settings": "", "Support Options": "", "Ticket Options": "", "Ticket Assign To": "", "Supporter View Type": "", "Per user [Default]": "", "Supporter see all": "", "Live Editor": "", "Template not found!": "", "Template can\\'t export. Please try again": "", "Import template ' . @$dataInput['title'] . ' success!": "", "Only support json file": "", "Your template has been saved": "", "Style Normal": "", "- Layout Normal: Background Color - get code in <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>": "", "- Layout 2&3 : Background Image Uploader": "", "Client Feedback": "", "Column": "", "FAQ List": "", "Question": "", "Answer": "", "Form Search All Service": "", "Title for :service": "", "Slider Carousel Ver 2": "", "Background Video": "", "- Layout Video: Youtube Url": "", "Title (using for slider ver 2)": "", "Desc (using for slider ver 2)": "", "Hide form search service?": "", "How It Works": "", "Image Uploader": "", "Style 5": "", "List Featured Item": "", "Offer Block": "", "Featured text": "", "Featured icon (find icon class in : https://icofont.com/icons)": "", "Section": "", "Editor": "", "Wrapper Class (opt)": "", "Padding": "", "Video Player": "", "Youtube link": "", "Template": "", "Edit Template:": "", "Create new template": "", "Template Name": "", "Search for block...": "", "Template Content": "", "You need to create the template at the Main-language tab first!": "", "Save Template": "", "Are you want to delete?": "", "Import Template": "", "All Templates": "", "Choose file": "", "Import": "", "Template Management": "", "Import new Template": "", "Add new Template": "", "All templates": "", "Export": "", "Clone": "", "Last saved:": "", "Save Block": "", "ADD LAYER": "", "Search block ...": "", "LAYERS": "", "Add layer": "", "Theme management": "", "Theme Upload": "", "Disable for demo mode": "", "Theme activated": "", "DEMO MODE: You are not allowed to do that": "", "This theme does not have seeder class": "", "Demo data has been imported": "", "Can not run data import": "", "Themes": "", "All Themes": "", "Do you want to import all demo data?": "", "Import Demo Data": "", "Last run: :date": "", "Activate": "", "Upload Theme": "", "Select theme file": "", "Select theme zip file:": "", "Maximum file size is: ": "", "Upload Now": "", "Tour": "", "Attributes: :name": "", "Term not found!": "", "Tours": "", "Booking": "", "Tour Booking History": "", "Tour Management": "", "Recovery Tour Management": "", "Add Tour": "", "Edit Tour": "", "Tour updated": "", "Tour created": "", "Select Category": "", "Image Background": "", "Service Tour": "", "Tour: Box Category": "", "Tour: Form Search": "", "Box Shadow": "", "Slider Carousel Simple": "", "Tour: List Items": "", "Tours Availability": "", "Tour not found": "", "Max guests: ": "", "Manage Tours": "", "Recovery Tours": "", "Restore tour success!": "", "Create Tours": "", "Tour not found!": "", "Edit Tours": "", "Delete tour success!": "", "Tour clone was successful": "", ":count tours found": "", ":count tour found": "", "Showing :from - :to of :total Tours": "", "Search for Tours": "", "There are :maxGuests guests available for your selected date": "", "This tour is not available at selected dates": "", "This tour is not open on your selected day": "", "There are :numberGuestsCanBook guests available for your selected date": "", "Not Rated": "", ":number Tours": "", ":number Tour": "", "Tour Type": "", "Tour Category": "", "All Tours": "", "Booking Calendar": "", "Manage Tour": "", "Tour Settings": "", "Tour Attributes": "", "Tours Availability Calendar": "", "No tours found": "", "Max Guest": "", "Min": "", "Max": "", "Tour Booking Calendar": "", "Tour Filters": "", "Showing :from - :to of :total Tour(s)": "", "Tour Categories": "", "Add new tour": "", "View Tour": "", "Tour Information": "", "SEO": "", "Tour Featured": "", "All Tour": "", "-- All Category --": "", "Config review for tour": "", "Enable review system for Tour?": "", "Turn on the mode for reviewing tour": "", "Customer must book a tour before writing a review?": "", "Does the review need approved by admin?": "", "Config buyer fees for tour": "", "Vendor config for tour": "", "Tour create by vendor must be approved by admin?": "", "Disable tour module?": "", "Disable tour module": "", "Fixed dates": "", "Enable Fixed Date": "", "Start Date": "", "Last Booking Date": "", "Open Hours": "", "Enable Open Hours": "", "Enable?": "", "Day of Week": "", "Open": "", "Tuesday": "", "Wednesday": "", "Thursday": "", "Friday": "", "Saturday": "", "Itinerary": "", "Title - Desc": "", "Title: Day 1": "", "Desc: TP. HCM City": "", "Tour Price": "", "Tour Sale Price": "", "Person Types": "", "Enable Person Types": "", "Person Type": "", "Eg: Adults": "", "Minimum per booking": "", "Maximum per booking": "", "per 1 item": "", "Tour Content": "", "Tour Min People": "", "Tour Max People": "", "Tour Locations": "", "Real tour address": "", "Tour information": "", "Tour name": "", "Discounts:": "", "from :from guests": "", ":from - :to guests": "", "Tracking Report": "", "Select at leas 1 item!": "", "Deleted!": "", "Updated successfully!": "", "Tracking": "", "Tracking Settings": "", "-- Event Type --": "", "Phone Click": "", "Website Click": "", "Enquiry Click": "", "Email Ads Click": "", "Ads Name": "", "-- Service Type --": "", "Campaign": "", "Service ID": "", "Lang": "", "Ip": "", "Payout ID": "", "CPC": "", "Tracking System": "", "Config tracking system option": "", "Enable Tracking": "", "Yes,please enable it": "", "Do not track these IP address": "", "Example: *************, **************": "", "User Plans": "", "User Plan Management": "", "Edit user plan": "", "Plan saved": "", "Plan Report": "", "Plan request management": "", "Users": "", "Select at lease 1 item!": "", "Deleted successfully!": "", "Roles": "", "Role updated": "", "Edit Role": "", "DEMO Mode: You can not do this": "", "Role created": "", "Role Management": "", "Verify Configs": "", "Field not found": "", "Edit field: :name": "", "Field created": "", "Field saved": "", "Permission Matrix": "", "Permission Matrix updated": "", "Subscribers": "", "Edit: :email": "", "Email exists": "", "Subscriber updated": "", "Edit User: #:id": "", "DEMO MODE: You can not change password!": "", "Your current password does not matches with the password you provided. Please try again.": "", "Password updated!": "", "Display name is a required field": "", "User updated": "", "User created": "", "Verify email successfully!": "", "Verify email cancel!": "", "Verification Request": "", "Verify request: :email": "", "User not found": "", "No verification field found": "", "Updated": "", "Add Credit": "", "Add credit for :name": "", ":amount credit added": "", "Credit purchase report": "", "Phone is required field": "", "Invoice": "", ":name send you message: :message": "", ":name send you file": "", "New Password cannot be same as your current password. Please choose a different password.": "", "Password changed successfully !": "", "Pricing Packages": "", "My Plan": "", "My plans": "", "This plan is not suitable for your role.": "", "This plan doesn't have annual pricing": "", "Please select payment gateway": "", "Purchased user package successfully": "", ":name - reviews from guests": "", "Reviews from guests": "", ":name - :type": "", ":type by :first_name": "", "Two Factor Authentication": "", "Profile": "", "The User name field is required.": "", "Thank you for subscribing": "", "You are already subscribed": "", "You have just done the become vendor request, please wait for the Admin's approved": "", "Request vendor success!": "", "Error. You can\\'t permanently delete": "", "Wishlist": "", "Service ID is required": "", "Service type is required": "", "Delete fail!": "", "Verification": "", "Update Verification Data": "", "The :name is required": "", "The :name path is required": "", "Verification data saved. Please wait for admin approval": "", "Verify code do not match": "", "Wallet": "", "Deposit option is not valid": "", "Deposit option amount is not valid": "", "Deposit option credit is not valid": "", "[:site_name] We updated your verification data": "", "[:site_name] Verify Register": "", "Verify Email Address": "", "[:site_name] Permanently Delete Account": "", "[:site_name] An user submitted verification data": "", "Vendor Registration Approved": "", "New Vendor Registration": "", "Business Name": "", "Address 2": "", "State": "", "Zip Code": "", "Your upgrade request has approved already": "", "Your has created a plan request": "", " has created a plan request": "", ":name has requested to become a vendor": "", " has been registered": "", ":name has requested a Credit Purchase : :amount": "", "Administrator has approved your Credit amount": "", "Your plan request has been approved": "", "Your plan request has been cancelled": "", " plan request has been approved": "", " plan request has been cancelled": "", "Your account information was verified": "", ":name has asked for verification": "", "Someone": "", "You have just gotten a new Subscriber": "", ":duration day": "", ":duration days": "", ":duration week": "", ":duration weeks": "", ":duration month": "", ":duration months": "", ":duration year": "", ":duration years": "", "week": "", "month": "", "year": "", "Users :count": "", "Role Manager": "", "Upgrade Request :count": "", "Verification Request :count": "", "User Plans :count": "", "Plan Request :count": "", "My Wallet": "", "Verifications": "", "Messages :count": "", "2F Authentication": "", "My Plans": "", "Wallet Settings": "", "User Settings": "", "User Plans Settings": "", "User Info": "", "Business name": "", "E-mail": "", "User name": "", "Phone Number": "", "Birthday": "", "Address Line 1": "", "Address Line 2": "", "Biographical": "", "Email Verified?": "", "Vendor Commission Type": "", "Disable Commission": "", "Vendor commission value": "", "Avatar": "", "Export to excel": "", "Verified": "", "Not Verified": "", "Verify email": "", "Email verified": "", "Old Password": "", "New password": "", "Re-Password": "", "Permission Content": "", "Add new permission": "", "All Permission": "", "Add Plan": "", "Plan Content": "", "name": "", "For Role": "", "Free": "", "Annual Price": "", "Duration Type": "", "Week": "", "Max Services": "", "Unlimited": "", "How many publish services user can post": "", "ID": "", "-- Select Employer --": "", " All Plan ": "", "Plan ID": "", "Plan Name": "", "Expiry": "", "Used/Total": "", "Expired": "", "Renew": "", "Mark as completed": "", "Mark as cancelled": "", "-- Status --": "", "-- User --": "", "Purchase logs": "", "Plan": "", "Name: :name": "", "Duration:  :duration_text": "", "Role Content": "", "Role Name": "", "Role Code": "", "Should be unique and letters only": "", "Add new role": "", "All Roles": "", "Manage Fields": "", "Add new field": "", "All Fields": "", "Icon": "", "For roles": "", "Required": "", "Edit Field: :name": "", "Edit verification field": "", "Field ID": "", "Field ID ": "", "Must be unique. Only accept letter and number, dash, underscore, without space": "", "Please enter field id and make sure it unique": "", "Field Name": "", "Please enter field name": "", "File attachment": "", "Multi files attachment": "", "Please enter field type": "", "For Roles?": "", "Please enter roles": "", "Is Required?": "", "Icon code": "", "Eg: fa fa-phone": "", "User Plans Options": "", "Config user plans page": "", "Enable User Plans": "", "Page Title": "", "Page Sub Title": "", "Sale Of Text": "", "Enable Multi User Plans": "", "Plan Request options": "", "Content email send to Customer or Administrator.": "", "New request plan": "", "Enable send email to Administrator?": "", "Subject": "", "Enable send email to customer?": "", "Update request plan": "", "Register Options": "", "Config register option": "", "Disable Registration?": "", "User Register Default Role": "", "Inbox System": "", "Config inbox option": "", "Allow customer can send message to the vendor on detail page": "", "Google reCapcha Options": "", "Config google recapcha for system": "", "Enable reCapcha Login Form": "", "Turn on the mode for login form": "", "Enable reCapcha Register Form": "", "Turn on the mode for register form": "", "Disable verification feature?": "", "Disable verification feature": "", "When two factor authentication feature is enabled, the user is required to input a six digit numeric token during the authentication process. This token is generated using a time-based one-time password (TOTP) that can be retrieved from any TOTP compatible mobile authentication application such as Google Authenticator.": "", "Content Email User Registered": "", "Content email send to Customer or Administrator when user registered.": "", "Email to customer content": "", "Content Email User Verify Registered": "", "Content email verify send to Customer when user registered.": "", "Enable must verify email when customer registered ?": "", "Content Email User Forgot Password": "", "Disable Wallet module?": "", "Disable wallet module": "", "Credit Options": "", "Credit exchange rate": "", "Exchange rate will be used in checkout page. Example: Credit * Exchange rate = Money": "", "Deposit Options": "", "Deposit type": "", "User input": "", "Select from lists": "", "Deposit rate": "", "Example: Money * Deposit rate = Credit": "", "Deposit lists": "", "Earn credit": "", "All amount will be in main currency": "", "New Credit Purchase Email Template": "", "Email for Admin": "", "Email for Customer": "", "Credit Purchase Updated Template": "", "Permanently delete account": "", "Permanently delete account will delete all services of that user and that user": "", "Content confirm": "", "Content Email Permanently delete account": "", "Content email verify send when user permanently deleted.": "", "To customer": "", "To admin": "", "Vendor Requests": "", "Role request": "", "Date request": "", "Date approved": "", "Approved By": "", "Approve": "", "Data": "", "Information": "", "Mark as verified": "", "Verification Requests": "", "All Verification": "", "View Verification": "", "View request": "", "Add credit": "", "Balance": "", "Credit Amount": "", "Add now": "", "Credit Purchase Report": "", "Hello :name": "", "You are receiving this email because we updated your vendor verification data.": "", "Not verified": "", "You can check your information here:": "", "View verification data": "", "Regards": "", "An user has been submit their verification data.": "", "You can approved the request here:": "", "You are receiving this email because we approved your vendor registration request.": "", "You can check your dashboard here:": "", "View dashboard": "", "Add new subscriber": "", "Subscriber Info": "", "Add Subscriber": "", "Search by name or email": "", "Payout Management": "", "Vendor Plans": "", "Plan created": "", "Vendor plan updated": "", "List Vendor": "", "Vendor Register Form": "", "Enquiry Report": "", "Enquiry not found!": "", "Payouts Management": "", "Vendor dashboard": "", "Payouts": "", "Your account information has been saved": "", "Sorry! No method available at the moment": "", "You does not select payout method or you need to enter account info for that method": "", "You don not have enough :amount for payout": "", "Minimum amount to pay is :amount": "", "Payout request has been created": "", "Can not create vendor message": "", "Team members": "", "Member does not exists": "", "You can not add yourself": "", "Request exists": "", "Request created": "", "A payout request has been updated": "", "Your payout request has been updated": "", "A vendor has been submitted a payout request": "", "Your payout request has been submitted": "", "A payout request has been deleted": "", "Your payout request has been deleted": "", "A payout request has been rejected": "", "Your payout request has been rejected": "", "Request join team": "", "Administrator has :action your PAYOUT request": "", ":name has sent a Payout request": "", "Initial": "", "Rejected": "", "Vendor Plan Meta": "", "User upgrade request": "", "Payouts :count": "", "Teams": "", "Vendor Settings": "", "Payout request management": "", "With selected:": "", "Bulk action": "", "Search by payout id": "", "Payout Method": "", "To admin:": "", "To vendor:": "", ":name to :info": "", "Payout request bulk action": "", "Pay date": "", "YYYY/MM/DD": "", "Note to vendor": "", "Please select at lease one item": "", "Do you want to delete those items?": "", "Status is empty": "", "Team Members": "", "Change your config vendor team members": "", "Team Member enable?": "", "Auto-approve team member request?": "", "Terms & Conditions": "", "Config Vendor": "", "Change your config vendor system": "", "Vendor Enable?": "", "Example value : 10 or 10.5": "", "Example: 10% commssion. Vendor get 90%, Admin get 10%": "", "Vendor Register": "", "Vendor Auto Approved?": "", "Vendor Role": "", "Vendor Profile": "", "Show vendor email in profile?": "", "Show vendor phone in profile?": "", "Payout Options": "", "Disable Payout Module?": "", "Booking Status Conditions": "", "Select booking status will be use for calculate payout of vendor": "", "Payout Methods": "", "Eg: bank_transfer": "", "Minimum to pay": "", "Content Email Vendor Registered": "", "Content email send to Vendor or Administrator when user registered.": "", "Email to vendor content": "", "Your payout request has been submitted:": "", "Your payout request has been updated:": "", "Your payout request has been rejected:": "", "Status:": "", "Pay date:": "", "Note to vendor:": "", "Payout information:": "", "Payout ID:": "", "Amount: ": "", "Payout method: ": "", "Note to admin: ": "", "Created at: ": "", "You can check your payout request here:": "", "View payouts": "", "Hello administrator": "", "A vendor has been submitted a payout request:": "", "A payout request has been updated:": "", "A payout request has been rejected:": "", "Vendor: ": "", "You can check all payout request here:": "", "Manage payouts": "", "Member Since :time": "", "View Profile": "", "All Booking": "", "Order Date": "", "Execution Time": "", "Payment Detail": "", "No Booking History": "", "Service Info": "", "Customer Info": "", "Vendor Payouts": "", "No payout methods available. Please contact administrator": "", "Payout history": "", "#": "", "Date Request": "", "Notes": "", "Date Processed": "", "Create request": "", "Balance: ": "", "Your balance is zero": "", "Create payout request": "", "Available for payout": "", "Method": "", "Minimum: :amount": "", "Note to admin": "", "Send request": "", "Setup your payment accounts": "", "Setup accounts": "", "To create payout request, please setup your payment account first": "", "Setup payout accounts": "", "Your account": "", "Your account info": "", "Vendor Teams": "", "As an author, you can add other users to your team. People on your team will be able to manage your services.": "", "Add someone to your team:": "", "Permissions": "", "Add": "", "Users on your team": "", "Send email": "", "Enable Two Checkout?": "", "Two Checkout": "", "Account Number": "", "Secret Word": "", "Gateway 2Checkout": "", "Gateway 2Checkout is one of the best payment Gateway to accept online payments from buyers around the world which allow your customers to make purchases in many payment methods, 15 languages, 87 currencies, and more than 200 markets in the world.": "", "Expiration Month": "", "Expiration Year": "", "Write a ": "", "OpenAI Settings": "", "API Key": "", "Model Name": "", "Magic text generator": "", "Keywords": "", "Some basic information or keywords": "", "Generate": "", "Use this content": "", "Booking status not valid": "", "Ticket not found": "", "This ticket does not belong to your events": "", "Ticket already scanned at :time": "", "Ticket scan success": "", "Ticket ID": "", "Show QR Code at the counter": "", "QR Code scanned at: :time": "", "Manage category": "", "Edit category": "", "Ticket Management": "", "Support does not exists": "", "Topic": "", "Support": "", "Topics": "", "Topic Management": "", "Duplicated": "", "Add Support": "", "Support updated": "", "Topic created": "", "All Tickets": "", "Search result for: :name": "", "My Tickets": "", "Create a ticket": "", "Ticket created": "", "Reply created": "", "Can not add reply": "", "All Topics": "", "New reply on ticket: #\" . $this->ticket->id))->view('Support::email.new_reply": "", "Topic Tag": "", "Closed": "", "Support Category": "", "Add Topic": "", "Edit topic: :name": "", "Add new topic": "", "All tickets": "", "Search topic": "", "Agent": "", "Unassigned": "", "View Ticket": "", "All topics": "", "Topic Tags": "", "Hello": "", "You got new reply for ticket: #": "", "Reply content:": "", "You can check the ticket here:": "", "View ticket": "", "View all": "", "Ticket Status": "", "Save Status": "", "User Notes": "", "Add note": "", "Add user note": "", "Old": "", "New": "", "Please provide ticket content": "", "Need Response": "", "Support tickets": "", "All categories": "", "How Can We Help?": "", "Find out more topics": "", "Support Tickets": "", "Search topic...": "", "Popular Topics": "", "Create new ticket": "", "Create ticket": "", "Ask a question": "", "Ticket name": "", "Last reply": "", "Showing :from - :to of :total tickets": "", "No ticket found": "", "Tags: ": "", "Related topics": "", "Showing :from - :to of :total topics": "", "No topic found": "", "These credentials do not match our records.": "", "Too many login attempts. Please try again in :seconds seconds.": "", "Laravel Installer": "", "Next Step": "", "Install": "", "The Following errors occurred:": "", "Welcome": "", "Easy Installation and Setup Wizard.": "", "Check Requirements": "", "Step 1 | Server Requirements": "", "Server Requirements": "", "Check Permissions": "", "Step 2 | Permissions": "", "Configure Environment": "", "Step 3 | Environment Settings": "", "Environment Settings": "", "Please select how you want to configure the apps <code>.env</code> file.": "", "Form Wizard Setup": "", "Classic Text Editor": "", "Step 3 | Environment Settings | Guided Wizard": "", "Guided <code>.env</code> Wizard": "", "Environment": "", "Database": "", "Application": "", "An environment name is required.": "", "App Name": "", "App Environment": "", "Local": "", "Development": "", "Qa": "", "Production": "", "Other": "", "Enter your environment...": "", "App Debug": "", "True": "", "False": "", "App Log Level": "", "debug": "", "info": "", "notice": "", "warning": "", "error": "", "critical": "", "alert": "", "emergency": "", "App Url": "", "Admin Password": "", "Database Connection": "", "mysql": "", "sqlite": "", "pgsql": "", "sqlsrv": "", "Database Host": "", "Database Port": "", "Database Name": "", "Database User Name": "", "Database Password": "", "More Info": "", "Broadcasting, Caching, Session, &amp; Queue": "", "Cache Driver": "", "Session Driver": "", "Queue Driver": "", "Redis Driver": "", "Redis Host": "", "Redis Password": "", "Redis Port": "", "Mail": "", "Mail Driver": "", "Mail Host": "", "Mail Port": "", "Mail Username": "", "Mail Password": "", "Mail Encryption": "", "Pusher": "", "Pusher App Id": "", "Pusher App Key": "", "Pusher App Secret": "", "Setup Database": "", "Setup Application": "", "Step 3 | Environment Settings | Classic Editor": "", "Classic Environment Editor": "", "Save .env": "", "Use Form Wizard": "", "Save and Install": "", "Your .env file settings have been saved.": "", "Unable to save the .env file, Please create it manually.": "", "Laravel Installer successfully INSTALLED on ": "", "Installation Finished": "", "Application has been successfully installed.": "", "Migration &amp; Seed Console Output:": "", "Application Console Output:": "", "Installation Log Entry:": "", "Final .env File:": "", "Click here to exit": "", "Laravel Updater": "", "Welcome To The Updater": "", "Welcome to the update wizard.": "", "Overview": "", "There is 1 update.|There are :number updates.": "", "Finished": "", "Application\\'s database has been successfully updated.": "", "Laravel Installer successfully UPDATED on ": "", "&laquo; Previous": "", "Next &raquo;": "", "Passwords must be at least eight characters and match the confirmation.": "", "Your password has been reset!": "", "This password reset token is invalid.": "", "Confirm password": "", "This is a secure area of the application. Please confirm your password before continuing.": "", "Please confirm access to your account by entering one of your emergency recovery codes.": "", "Recovery Code": "", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "", "Use an authentication code": "", "Use a recovery code": "", "Unauthorized": "", "Forbidden": "", "Page not found": "", "Sorry, we couldn't find the page you're looking for.": "", "Page Expired": "", "Too Many Requests": "", "Server Error": "", "Service Unavailable": "", "Oh no": "", "Go Home": "", "installer_messages.environment.classic.templateTitle": "", "installer_messages.environment.classic.title": "", "installer_messages.environment.classic.save": "", "installer_messages.environment.classic.back": "", "installer_messages.environment.classic.install": "", "installer_messages.environment.wizard.templateTitle": "", "installer_messages.environment.wizard.title": "", "Site Name": "", "installer_messages.environment.wizard.form.app_name_placeholder": "", "installer_messages.environment.wizard.form.app_environment_label": "", "installer_messages.environment.wizard.form.app_environment_label_local": "", "installer_messages.environment.wizard.form.app_environment_label_developement": "", "installer_messages.environment.wizard.form.app_environment_label_qa": "", "installer_messages.environment.wizard.form.app_environment_label_production": "", "installer_messages.environment.wizard.form.app_environment_label_other": "", "installer_messages.environment.wizard.form.app_environment_placeholder_other": "", "installer_messages.environment.wizard.form.app_debug_label": "", "installer_messages.environment.wizard.form.app_debug_label_true": "", "installer_messages.environment.wizard.form.app_debug_label_false": "", "installer_messages.environment.wizard.form.app_log_level_label": "", "installer_messages.environment.wizard.form.app_log_level_label_debug": "", "installer_messages.environment.wizard.form.app_log_level_label_info": "", "installer_messages.environment.wizard.form.app_log_level_label_notice": "", "installer_messages.environment.wizard.form.app_log_level_label_warning": "", "installer_messages.environment.wizard.form.app_log_level_label_error": "", "installer_messages.environment.wizard.form.app_log_level_label_critical": "", "installer_messages.environment.wizard.form.app_log_level_label_alert": "", "installer_messages.environment.wizard.form.app_log_level_label_emergency": "", "Site url": "", "installer_messages.environment.wizard.form.app_url_placeholder": "", "installer_messages.environment.wizard.form.buttons.setup_database": "", "installer_messages.environment.wizard.form.db_connection_label": "", "installer_messages.environment.wizard.form.db_connection_label_mysql": "", "installer_messages.environment.wizard.form.db_connection_label_sqlite": "", "installer_messages.environment.wizard.form.db_connection_label_pgsql": "", "installer_messages.environment.wizard.form.db_connection_label_sqlsrv": "", "installer_messages.environment.wizard.form.db_host_label": "", "installer_messages.environment.wizard.form.db_host_placeholder": "", "installer_messages.environment.wizard.form.db_port_label": "", "installer_messages.environment.wizard.form.db_port_placeholder": "", "installer_messages.environment.wizard.form.db_name_label": "", "installer_messages.environment.wizard.form.db_name_placeholder": "", "installer_messages.environment.wizard.form.db_username_label": "", "installer_messages.environment.wizard.form.db_username_placeholder": "", "installer_messages.environment.wizard.form.db_password_label": "", "installer_messages.environment.wizard.form.db_password_placeholder": "", "installer_messages.environment.wizard.form.app_admin_email_placeholder": "", "installer_messages.environment.wizard.form.app_admin_password_placeholder": "", "Test DB": "", "installer_messages.environment.wizard.form.buttons.install": "", "installer_messages.environment.wizard.form.app_tabs.broadcasting_title": "", "installer_messages.environment.wizard.form.app_tabs.broadcasting_label": "", "installer_messages.environment.wizard.form.app_tabs.more_info": "", "installer_messages.environment.wizard.form.app_tabs.broadcasting_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.cache_label": "", "installer_messages.environment.wizard.form.app_tabs.cache_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.session_label": "", "installer_messages.environment.wizard.form.app_tabs.session_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.queue_label": "", "installer_messages.environment.wizard.form.app_tabs.queue_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.redis_label": "", "installer_messages.environment.wizard.form.app_tabs.redis_host": "", "installer_messages.environment.wizard.form.app_tabs.redis_password": "", "installer_messages.environment.wizard.form.app_tabs.redis_port": "", "installer_messages.environment.wizard.form.app_tabs.mail_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_driver_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_driver_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_host_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_host_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_port_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_port_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_username_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_username_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_password_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_password_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.pusher_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_palceholder": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_palceholder": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_palceholder": "", "installer_messages.environment.menu.templateTitle": "", "installer_messages.environment.menu.title": "", "installer_messages.environment.menu.desc": "", "installer_messages.environment.menu.wizard-button": "", "installer_messages.environment.menu.classic-button": "", "installer_messages.final.templateTitle": "", "installer_messages.final.title": "", "installer_messages.final.log": "", "installer_messages.final.exit": "", "installer_messages.updater.title": "", "installer_messages.title": "", "installer_messages.forms.errorTitle": "", "installer_messages.permissions.templateTitle": "", "installer_messages.permissions.title": "", "installer_messages.permissions.next": "", "installer_messages.requirements.templateTitle": "", "installer_messages.requirements.title": "", "installer_messages.requirements.next": "", "installer_messages.updater.final.title": "", "installer_messages.updater.final.exit": "", "installer_messages.updater.welcome.title": "", "installer_messages.updater.overview.message": "", "installer_messages.updater.overview.install_updates": "", "installer_messages.updater.welcome.message": "", "installer_messages.next": "", "installer_messages.welcome.templateTitle": "", "Booking Core :version Installer": "", "installer_messages.welcome.message": "", "installer_messages.welcome.next": "", "Log file >50M, please download it.": "", "Context": "", "Line number": "", "pagination.previous": "", "pagination.next": "", "Booking ID": "", "Booking Detail": "", "Customer Information": "", "Your Booking": "", "Start date:": "", "Total:": "", "Paid:": "", "Remain:": "", "End date": "", "Durations": "", "Details": "", "1. Content": "", "2. Locations": "", "3. Pricing": "", "4. Attributes": "", "No Boat": "", "Last Updated": "", "\"Do you want to recovery?\"": "", "\"Do you want to permanently delete?\"": "", "Del": "", "\"Do you want to delete?\"": "", "Make hide": "", "Make publish": "", "Boat by :name": "", "View all (:total)": "", "You got reply from vendor. ": "", "Service:": "", "Your note:": "", "Here is the message from vendor:": "", "New booking has been made": "", "Your service has new booking": "", "Thank you for booking with us. Here are your booking information:": "", "Customer information": "", "Tickets / Guests Information:": "", "Guest #:number": "", "First Name: ": "", "Last Name: ": "", "Email: ": "", "Phone: ": "", "The booking status has been updated": "", "Personal Information": "", "Guests Information": "", "Number:": "", "5. Ical": "", "No Car": "", "Car by :name": "", "100$": "", "Bonus 10%": "", "Bonus 15%": "", "only for Services": "", "Manage Coupon": "", "Add Coupon": "", "Showing :from - :to of :total coupon": "", "No Coupon": "", "Print Ticket": "", "Event by :name": "", ":from to :to": "", ":duration hrs": "", "Arrival Time ": "", "No Flight": "", "Do you want to permanently delete?": "", "Add new seat": "", "Manage Seats": "", "1. Seat Content": "", "Back to flight": "", "Add Seat": "", "Flight id": "", "Flight by :name": "", "Check in:": "", "Adults:": "", "Children:": "", "Check out": "", "Hotel by :name": "", "Recovery news": "", "Manage news": "", "No Space": "", "Space by :name": "", "There is no layer yet!": "", "Click button bellow to start adding layer": "", "4. Availability": "", "5. Attributes": "", "6. Ical": "", "No Tours": "", "Tour by :name": "", "Setup Two Factor Authentication": "", "You have enabled factor authentication": "", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "", "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.": "", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "", "Disable two factor authentication": "", "You have not enabled factor authentication": "", "Enable now": "", "Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in": "", "INVOICE": "", "Invoice #: :number": "", "Created: :date": "", "Amount due:": "", "Billing to:": "", "Current Password": "", "New Password": "", "* Require at least one uppercase, one lowercase letter, one number and one symbol.": "", "New Password Again": "", "My Profile": "", "Become a vendor": "", "Log Out": "", "Back to Homepage": "", "Choose your pricing plan": "", "Save up to 10%": "", "Monthly": "", "Annual": "", "Recommended": "", "Current Plan": "", "Repurchase": "", "Select": "", "My Current Plan": "", "No Items": "", "Hi, I'm :name": "", "View all reviews (:total)": "", ":count review": "", ":count reviews": "", "About Yourself": "", "Browse": "", "Error upload...": "", "No Image": "", "Location Information": "", "Address2": "", "Delete account": "", "Your account will be permanently deleted. Once you delete your account, there is no going back. Please be certain.": "", "Delete your account": "", "Confirm permanently delete account": "", "Select File": "", "N/A": "", "Select Files": "", "Verify Phone": "", "Verification data": "", "Update verification data": "", "Verify": "", "Buy": "", "Sorry, no options found": "", "How much would you like to deposit?": "", "Deposit amount": "", "Process now": "", ":amount": "", "Latest Transactions": "", "Gateway": "", "Deposit by :name": "", "WishList": "", "Showing :from - :to of :total": "", ":number Reviews": "", ":number Review": "", "Remove": "", "Advanced Filter": "", "Customer name": "", "Customer Name": "", "From - To": "", "We couldn't find any boats.": "", "Try changing your filter criteria": "", "Please select start date": "", "Please select at least one number": "", "Name is Required": "", "Email is Required": "", "Boat Video": "", "from :number reviews": "", ":number% of guests recommend": "", "Length Boat": "", "from": "", "/per hour": "", "/per day": "", "Book Now": "", "Contact Now": "", "Book": "", "Return on same-day": "", "Return on another day": "", "Days": "", "Select Dates": "", "Book :number days in advance": "", "Book :number day in advance": "", "Extra prices:": "", "BOOK NOW": "", "Included": "", "Excluded": "", "Included/Excluded": "", "You might also like": "", "Specs & Details": "", "All :name": "", "Where are you going?": "", "Search for...": "", "FILTER BY": "", "APPLY": "", "Sort by:": "", "Price (Low to high)": "", "Price (High to low)": "", "Rating (High to low)": "", "Apply Filters": "", "Price filter": "", "More filters": "", "Availability Boats": "", "We couldn't find any cars.": "", "Clear Filters": "", "Please select Start and End date": "", "Car Video": "", "Stay at least :number days": "", "Stay at least :number day": "", "/day": "", "Availability Cars": "", "Header Settings": "", "Enable Header Sticky": "", "We couldn't find any events.": "", "Event Video": "", "People interest: :number": "", "per ticket": "", "Start Time: :time": "", "Availability Events": "", "No Event": "", "Flight not found": "", "Please select at least one guest": "", "View on map": "", "People": "", "bathrooms": "", "beds": "", "Ages 12+": "", "Ages 2–12": "", ":count Guest in maximum": "", ":count Guests in maximum": "", "You might also like...": "", "Video": "", "1 Adult": "", ":count Adults": "", ":count Child": "", ":count Children": "", "City or airport": "", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu select-seat-type-dropdown\" >\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n\t\t\t\t$inputName = 'seat_type_'.$type->code;\r\n\t\t\t\t$inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n\t\t\t\t;?>\r\n\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Adults :type": "", "avg/person": "", "Take off": "", "Landing": "", "Choose": "", "Check-in": "", "Pay Amount": "", "We couldn't find any spaces.": "", "Availability Spaces": "", "We couldn't find any hotels.": "", "Hotel Video": "", "Rules": "", "Check In": "", "Check Out": "", "Hotel Policies": "", "Show All": "", "Related Hotel": "", "/night": "", "Available Rooms": "", "Check In - Out": "", "Check Availability": "", "Room Footage": "", "No. Beds": "", "No. Adults": "", "No. Children": "", "Total Room": "", "Total Price": "", "No room available with your selected date. Please change your search critical": "", "What's Nearby": "", "Availability Rooms": "", "No Hotel": "", "Add new room": "", "1. Room Content": "", "2. Pricing": "", "3. Attributes": "", "4. Ical": "", "Showing :from - :to of :total Rooms": "", "No Room": "", "Upgrade to PRO to unlock unlimited access to all of our features": "", "Explore the place": "", "The City Maps": "", "FEATURED ARTICLE": "", "Read More": "", "Showing :from - :to of :total posts": "", "Sorry, but nothing matched your search terms. Please try again with some different keywords.": "", "BY ": "", "DATE ": "", "Tags:": "", "Search ...": "", "Oops! It looks like you're lost.": "", "The page you're looking for isn't available. Try to search again or use the go to.": "", "Go back to homepage": "", "Space Video": "", "No. People": "", "We couldn't find any tours.": "", "Book now": "", "Please select Start date": "", "Tour Video": "", "Group Size": "", ":number persons": "", ":number person": "", "Tour Location": "", "Tour Start Date": "", "Tour End Date": "", "per person": "", "Message host": "", "Availability Tours": "", "Showing :from - :to of :total tours": "", "Enable Preload": "", "Logo Dark": "", "Page 404 settings": "", "Settings for 404 error page": "", "Error 404 banner": "", "Error title": "", "Error desc": "", "FEATURE": "", ":count enrolled on this course": "", "Last updated :date_update": "", "Add To Cart": "", "Buy Now": "", "Lessons": "", "Quizzes": "", "Skill level": "", "Instructor": "", "Instructor Rating": "", "You May Like": "", "10,000+ unique online course list designs": "", "Expand All Sections": "", "Show more": "", "All Levels": "", "5": "", "4.0 & up": "", "3.0 & up": "", "2.0 & up": "", "1.0 & up": "", "FEATURED": "", "lesson": "", "lessons": "", "Showing": "", "total results": "", "Newest": "", "Oldest": "", "Price [high to low]": "", "Price [low to high]": "", "Rating [high to low]": "", "Explore": "", "Log in": "", "Call us": "", "Sign up": "", "My Courses": "", "Popular Right Now": "", "PRESS ENTER TO SEE ALL SEARCH RESULTS": "", "Banner Sub Title": "", "Enable review system for News?": "", "Turn on the mode for reviewing news": "", "Fb": "", "Tw": "", "Linkedin": "", "Ln": "", "Pinterest": "", "Pin": "", "Prev": "", "Related Posts": "", "What are you looking for?": "", "All Categories": "", "Don't have an account yet?": "", "Sign up for free": "", "Already have an account?": "", "Go Back To Homepage": "", "Leave A Review": "", "Enter Title": "", "Rating": "", "Five Star": "", "Four Star": "", "Three Star": "", "Two Star": "", "One Star": "", "Submit Review": "", "reviews": "", "review": "", ":from - :to of :total+ :review available": "", "Agent Single": "", " Listings": "", "Mobile: ": "", "View My Listing": "", "Listing": "", "Your Name": "", "Your Message": "", "Search results": "", "Sort by": "", "Name ( a -> z )": "", "Name ( z -> a )": "", "Listings": "", "Advanced Search": "", "Hide Filter": "", "All Agents": "", "Find Agent": "", "Enter Agent Name": "", "Agency": "", "(:rate_agv out of 5)": "", "Write a Review": "", "Your Rating & Review": "", "Min Area": "", "Max Area": "", "Advanced features": "", "Show Filter": "", "Agents": "", "Search Agent": "", "Create Agent": "", "Search Agency": "", "Send Message": "", "Become a Real Estate Agent": "", "We only work with the best companies around the globe": "", "Register Now": "", "Contact Information": "", "Logo Transparent": "", "Logo Mobile": "", "Contact Title": "", "Contact Sub Title": "", "Contact Banner": "", "Enable reCapcha Form": "", "Turn on the mode for contact form": "", "Contact Locations": "", "Contact partners": "", "Contact partner title": "", "Contact partner sub title": "", "Contact partner button text": "", "Contact partner button link": "", "Views": "", "Total Views": "", "Total Visitor Reviews": "", "Favorites": "", "Total Favorites": "", "Recent Activities": "", "Login with Facebook": "", "Login with Google": "", "Login with Twitter": "", "Lost your password?": "", "I have read and accept the Terms and Privacy Policy?": "", "Already have an account? ": "", "Agent Dashboard": "", "Create Listing": "", "Dashboard Navigation": "", "© 2020 Find House. Made with love.": "", "View All": "", "views": "", "property": "", "properties": "", "/mo": "", "Beds:": "", "Baths:": "", "Sq Ft:": "", "Search Here": "", "Scroll Down": "", "to discover more": "", "Beds": "", "Baths": "", "Sq Ft": "", "Show More": "", "Property Details": "", "Property ID": "", "Property Size": "", "None": "", "Garage": "", "Property Status": "", "Pool Size": "", "Additional Rooms": "", "Last remodel year": "", "Listed By": "", "View Photos": "", "Similar Properties": "", "Hide\r\n                                    Filter": "", "List Property": "", ":count properties found": "", ":count property found": "", "Name [a->z]": "", "Name [z->a]": "", "Sq:": "", "Search by keyword": "", "All Type": "", "We couldn't find any properties.": "", "No Booking": "", "phone": "", "email": "", "Property Image": "", "Add property": "", "Search Properties": "", "Listing Title": "", "Date published": "", "Property by :name": "", "Featured Properties": "", "Recently Viewed": "", "Availability Properties": "", "Layout 1": "", "Layout 2": "", "- Style Slider: List Item(s)": "", "Select property": "", "Hide Slider Controls": "", "Banner Property": "", "Show Attribute": "", "Style 2 - Slider Carousel": "", "Style 6": "", "Style 7": "", "- Style: Background Image Uploader": "", "Video URL": "", "Gallery Images": "", "Video Url (Youtube, Vimeo, ..)": "", "Icon Class": "", "Image Text With Counting": "", "List Location by ID": "", "Property Map by location": "", "Style 2 : With Background Image": "", "Background Image Style 4": "", "Custom Title": "", "Background Image": "", "Page Banner": "", ", Baths:": "", ", Sq:": "", "Learn More": "", "Enter keyword ...": "", "Choose Price": "", "Hide": "", "Rent": "", "You have just done the become agent request, please wait for the Admin\\'s approved": "", "Manage Contacts": "", "--Select Filter--": "", "Property contact": "", "Agent contact": "", "Agency contact": "", "Object": "", "Guest name": "", "Created at": "", "') }}\r\n                                            @endif\r\n\r\n                                            @if ($row->object_model == 'property": "", "Property views": "", "Become a agent": "", "User Social": "", "Scroll Down ID": "", "List Layout": "", "Grid Layout": "", "About": "", "Sale off :number": "", "Starting from": "", "per adult": "", "View Detail": "", "Clear All": "", "Lorem ipsum dolor sit amet, consectetur.": "", "Sign in": "", "to book with your saved details or": "", "register": "", "to manage your bookings on the go!": "", "Let us know who you are": "", "List Car by IDs": "", "Map Background in Button Show Map": "", "Property highlights": "", "Select Number": "", "See All": "", "Show on map": "", "Send a message": "", "Full Name": "", "Your Messages": "", "Send a Messsage": "", "List Contact": "", "Info Contact": "", "Iframe google map": "", "Why Choose Us": "", "Block: Title": "", "Block: Desc": "", "List Items": "", "Title/Desc": "", "Footer Style": "", "Style 8": "", "Footer content left": "", "Footer content right": "", "Logo Preload": "", "Do you have a promo code?": "", "Enter promo code": "", "Select term event": "", "Event: Term Feature Box": "", "Term Icon": "", "Event snapshot": "", ":number% of travelers recommend this experience": "", "Start Time: :time - ": "", "Destinations": "", "Price Filter": "", "Service Flight": "", "Showing :from - :to of :total flights": "", "- List Item(s)": "", "Discover Title": "", "Discover Link": "", "Normal 2": "", "Slider Carousel V2": "", "Showing :from - :to of :total hotels": "", "Scroll Now": "", "Select Room": "", "Room Type": "", "Benefits": "", "Select Rooms": "", "Show Room Information": "", "adults": "", "children": "", "Hotel Rules - Policies": "", "See Availability": "", "Welcome back": "", "Sign In": "", "or sign in with": "", "By creating an account, you agree to our Terms of Service and Privacy Statement.": "", "Sign in or create an account": "", "FAQs about": "", "See All :count Photos": "", "Guest reviews": "", "Leave a Reply": "", "Your email address will not be published.": "", "Write Your Comment": "", "Post Comment": "", "You must <a href='#login' data-bs-toggle='modal' data-target='#login'>log in</a> to write review": "", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                    </span>\r\n                @endforeach\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"searchMenu-guests__field select-seat-type-dropdown shadow-2\" data-x-dd=\"searchMenu-guests\" data-x-dd-toggle=\"-is-active\">\r\n        <div class=\"bg-white px-30 py-30 rounded-4\">\r\n            @foreach($seatType as $type)\r\n                <?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ?>\r\n\r\n                <div class=\"row y-gap-10 justify-between items-center\">\r\n                    <div class=\"col-auto\">\r\n                        <div class=\"text-15 fw-500\">{{__('Adults :type": "", "Where you’ll be": "", "Your Travel Journey Starts Here": "", "Sign up and we'll send the best deals to you": "", "Main Menu": "", "Become An Expert": "", "Sign In / Register": "", "MENU": "", "Style 9": "", "Style 10": "", "View All Url": "", "General info": "", "Desc:": "", "View All Destinations": "", "Discover": "", "travellers": "", "Explore deals, travel guides and things to do in :text": "", "Interdum et malesuada fames ac ante ipsum": "", "What to know before visiting :text": "", "Most Popular :name": "", "Top sights in  :text": "", "These popular destinations have a lot to offer": "", "See More": "", "Header Align": "", "Left": "", "Center": "", "Right": "", "Related content": "", "Interdum et malesuada fames": "", "Normal White": "", "Transparent V2": "", "Transparent V3": "", "Transparent V4": "", "Transparent V5": "", "Transparent V6": "", "Transparent V7": "", "Transparent V8": "", "Transparent V9": "", "Disable subscribe default": "", "Bathroom": "", "nights": "", "guests": "", ":count :guest": "", ":count bathroom": "", ":count bed": "", "per night": "", "About Text": "", "Subtitle": "", "Link Download": "", "Download App": "", "Normal Ver 2": "", "Slider Ver 2": "", "List All Service": "", "Login Register": "", "OverLay": "", "Subscribe Style": "", "List Terms": "", "Text Featured Box": "", "Text Image": "", "Image 1": "", "Image 2": "", "Style 4 (Only List Item)": "", "Style 5 (Only List Item)": "", "Youtube Image": "", "Youtube Link": "", "Job": "", "Happy people number": "", "Happy people text": "", "Overall rating number": "", "Overall rating text": "", "Overall rating star": "", "Title Trusted": "", "List Trusted(s)": "", "Logo Image": "", "Testimonial Background (For Style 4, Style 6, Style 7)": "", "Book Title": "", "Book Desc": "", "Book Url": "", "Book Url Text": "", "Book Image": "", "Tour: Tour Deals": "", "Tour: Tour Types": "", "Category Icon Class": "", "Category icon": "", "Watch Video": "", ":count Tours": "", "Tour snapshot": "", "Important information": "", "Not sure? You can cancel this reservation up to 24 hours in advance for a full refund.": "", "See less": "", "See details & photo": "", "Ready to jump back in?": "", "Earning Statistics": "", "No booking": "", "Your avatar": "", "PNG or JPG no bigger than 800px wide and tall.": "", "User Name": "", "Page become an expert": "", "Website": "", "Founded Time": "", ":from - :to of :total+ :agent available": "", "Company Agent at ": "", "Show less": "", "For Sale": "", "Professional Information": "", "Agency address": "", "Contact Form": "", "There are many variations of passages.": "", "Enter agent name": "", "Name (\r\n                                     a -> z )": "", "Name (\r\n                                     z -> a )": "", ":from - :to of :total+ :property available": "", ":from - :to of :total+ :agency available": "", "Agency Information": "", "Broker address": "", "Websites": "", "Member since": "", "View Listings": "", "All Agencies": "", "Enter agency name": "", "All Cities": "", "Name (a -> z)": "", "Name (z -> a)": "", "Link Map": "", "Form Title": "", "Form Sub Title": "", "Textarea": "", "Footer Content": "", "Phone Contact": "", "Currency:": "", "OR": "", "Continue Google": "", "Continue Facebook": "", "Continue Apple": "", "Not signed up? ": "", "Create an account.": "", "Email Address": "", "Password Confirm": "", "Create account": "", "Done": "", "Grid": "", "List": "", "Keep Yourself Up to Date": "", "Login / Register": "", "MANAGE LISTINGS": "", "Add New Property": "", "My Properties": "", "MANAGE ACCOUNT": "", "Welcome to": "", "New Account": "", "Number Items": "", "Button Title": "", "Button Link": "", "Location Blocks": "", "See All Properties": "", "View City": "", "See All Cities": "", "Share this post": "", "Previous Post": "", "Next Post": "", ":from - :to of :total+ properties available": "", ":from - :to of :total+ news available": "", "We are glad to see you again!": "", "Banner Image 1": "", "Banner Image 2": "", "Select Attributes": "", "Category Title": "", "Category Limit": "", "Select Categories": "", "Link Video Ember": "", "Select Properties": "", "List Counter": "", "Plus": "", "Class Wrapper": "", "Form Search": "", "Background Color": "", "List Categories": "", "Image Upload": "", "List Featured Properties": "", "Filter by category": "", "Show Type": "", "Both": "", "If left blank the button will be hidden": "", "Custom Class (optional)": "", "Background": "", "List Properties": "", "Bedroom": "", "Bath": "", "Sqft": "", "Floor Plans": "", "Bed": "", "Sidebar": "", "Map Search Layout": "", "Grid Cols": "", "Grid Style": "", "Search desc": "", "Search Background Image": "", "Sidebar detail page": "", "Vendor information": "", "Vendor contact": "", "Property search": "", "Property featured": "", "More Filter": "", "Price Range": "", "any": "", "Reset all filters": "", "Enter a property name or an address": "", "Enter Keyword": "", "Any Category": "", "Popular Searches": "", "Search products…": "", "sq ft": "", "View Details": "", "Enter Keywords": "", "Looking For": "", "Any Location": "", "View detail": "", "See All Categories": "", "FOR SALE": "", ":count bath": "", "Total Free Customer Care": "", "Nee Live Support?": "", "bed": "", "bath": "", "See All :total Photos": "", "Property Description": "", "Size:": "", "Property Showcase": "", "Related Properties": "", "Request Information": "", "Contact vendor": "", "Enter Your Messages": "", "Get More Information": "", "Contact Agent": "", ":number bed": "", ":number bath": "", "Features & Amenities": "", "Find your home": "", "All\r\n                        Type": "", "any bath": "", "any bed": "", "Any": "", "Is Sold": "", "Listing title": "", "Date Published": "", "Deletel": "", "Sign in with this account across the following sites.": "", "Go Back To Homepages": "", "Image Uploader 2": "", "Image Uploader 3": "", "Icon Class - get class in <a href=\"https://www.flaticon.com\" target=\"_blank\">https://www.flaticon.com</a>": "", "Title for list": "", "First item is main item": "", "Link to": "", "Block Plans": "", "Saving": "", "List Plan(s)": "", "Filter by Plan": "", "Block Teams": "", "List Team(s)": "", "Link": "", "Brands List": "", "Custom Class": "", "List Brand(s)": "", "Brand Logo": "", "Url": "", "Class (css)": "", "Button Name 2": "", "Button Url 2": "", "Margin": "", "Counter": "", "Button Name": "", "Button Url": "", "Block Download App": "", "List App(s) Download": "", "Link download": "", "Fun Fact": "", "Animate": "", "Icon Box": "", "Is Featured?": "", "Text color": "", "List Fact Item(s)": "", "Why Choose us": "", "Featured Text": "", "Featured Value": "", "List Text Item(s)": "", "Featured icon": "", "Billed Monthly": "", "Billed Yearly": "", "per month": "", "Join": "", "Password Confirm is required field": "", "Pricing Icon": "", "Filter: ": "", "Manage Account": "", "Membership Plans": "", "Save up to 20%": "", "Upload Profile Files": "", "Photos must be JPEG or PNG format and least 2048x768": "", "About me": "", "Social Information": "", "--Select Icon--": "", "Update Profile": "", "My Favorites": "", "No Wishlist": "", "Your trip": "", "About this boat": "", "Boat's Location": "", "Frequently asked questions": "", "/ per hour": "", "/ per day": "", "Explore other options": "", "Add to wishlist": "", "Check": "", "(:number Reviews)": "", "(:number Review)": "", "Layout Detail": "", "Seats": "", "Gear": "", "About this car": "", "Car's Location": "", "List Item(s) Contact": "", "Error 404 background image": "", "About this event": "", "Event Location": "", "All photo": "", "View in a map": "", "About this hotel": "", "Reserve a room": "", "Owner": "", "Enter Email": "", "Forgot Password": "", "Create an account": "", "Enter First Name": "", "Enter Last Name": "", "Enter Phone": "", "Enter Password": "", "I confirm that I have read and accepted the privacy policy": "", "Already have an account": "", "Information Contact": "", "Show on the list": "", "Sign in to your account": "", "Block subscribe settings": "", "Settings for block subscribe": "", "Subscribe title": "", "Subscribe sub title": "", "Subscribe Image": "", "Stories, tips, and guides": "", "Post navigation": "", "BY": "", "404 Page": "", "Go to home": "", "View Less": "", "Leave a review": "", "Required fields are marked": "", "About this rental": "", "Rental's Location": "", "List About": "", "Other Blocks": "", "List Item(s) Right": "", "Google Map Block": "", "Iframe Google Map": "", "Icon Image": "", "Icon Class - get class in <a href=\"https://fontawesome.com/v4/icons/\" target=\"_blank\">https://fontawesome.com/v4/icons/</a>": "", "Number Star": "", "Video Block": "", "Video Link": "", "About this tour": "", "Payment": "", "Fee:": "", "Includes": "", "Excludes": "", "Filter Search": "", "Other Settings": "", "Why Book With Us?": "", "Class icon": "", "Customer care available 24/7": "", "Title - Link info": "", "By continuing, you agree to the": "", "Terms and Conditions": "", "CONFIRM BOOKING": "", "Your Card Information": "", "Thank You. Your booking was submitted successfully!": "", "Your booking status is: :status": "", "Car Blocks": "", "Car: List Term Items": "", "Specifications": "", "Specifications List": "", "Specifications Desc": "", "Specifications name": "", "SAVE :text": "", "Pick Up Date": "", "Save for later": "", "View On Map": "", "Sends us a Message": "", "Link View on Map": "", "Footer Info Contact": "", "Logo Color": "", "Logo Text": "", "List Event by IDs": "", "Event Blocks": "", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu custom-select-dropdown\">\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ;?>\r\n\t\t\t\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Adults :type": "", "Show all": "", "Flight Details": "", "List Hotel by IDs": "", "Hotel Blocks": "", "Badge tag": "", "Eg: service VIP": "", "Brown": "", "Maroon": "", "Green": "", "Danger": "", "Info": "", "Dark": "", "Eg: Service VIP": "", "night": "", "Address-Description": "", "Select Your Room": "", "Room": "", "Mailing List": "", "Sign up for our mailing list to get latest updates and offers.": "", "Page navigation": "", "Sign in or Register": "", "First row 2 cards": "", "Style 3 cards/ row": "", "First row 3 cards": "", "Slide 4 cards/slider": "", "Style 5 cards/ row": "", "Location Name": "", "Location Desc": "", "Location Button Text": "", "Location Button Link": "", "Number Item (Default: 4)": "", "Unmissable Destinations": "", "Welcome to :name": "", "Top Experiences in :name": "", "Recent articles": "", "Read More Articles": "", "Company or title": "", "List Space by IDs": "", "Space Blocks": "", ":num :text": "", "List Brand Item(s)": "", "Breadcrumb Section": "", "Background Gradient overlay": "", "Grayish Blue": "", "Blue Light": "", "Orange": "", "Tab button Pills": "", "Tab button Boxed": "", "Tab button Shadow": "", "- Style 1: Background Image Uploader": "", "single form search": "", "- Style 1 : Image Uploader": "", "- Style 2, Style 3 : Icon Class": "", "Video Caption": "", "List Tour by IDs": "", "Tour Blocks": "", "Date From-To": "", "Min age": "", "Pickup": "", "Wifi available": "", "Max People": "", "Wifi Available": "", "Min Age:": "", "Pickup:": "", "$row->pickup": "", "Price Includes": "", "Price Excludes": "", "auth.failed": "", "The provided two factor authentication code was invalid.": "", "The provided password was incorrect.": "", "The provided two factor recovery code was invalid.": "", "auth.throttle": "", "The :attribute must be at least :length characters and contain at least one uppercase character.": "", "The :attribute must be at least :length characters and contain at least one number.": "", "The :attribute must be at least :length characters and contain at least one special character.": "", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "", "The :attribute must be at least :length characters and contain at least one special character and one number.": "", "The :attribute must be at least :length characters.": "", "Reset Password Notification": "", "You are receiving this email because we received a password reset request for your account.": "", "This password reset link will expire in :count minutes.": "", "If you did not request a password reset, no further action is required.": "", "Please click the button below to verify your email address.": "", "If you did not create an account, no further action is required.": "", "Payment Required": "", "All rights reserved.": "", "Whoops!": "", "Hello!": "", "to": "", "of": "", "results": "", "Pagination Navigation": "", "Go to page :page": "", "There was an error on row :row. :message": "", "diff_now": "", "diff_yesterday": "", "diff_tomorrow": "", "diff_before_yesterday": "", "diff_after_tomorrow": "", "period_recurrences": "", "period_interval": "", "period_start_date": "", "period_end_date": "", "validation.phone": "", "installer_messages.final.finished": "", "installer_messages.environment.success": "", "installer_messages.environment.errors": "", "installer_messages.installed.success_log_message": "", "installer_messages.updater.log.success_message": "", "installer_messages.environment.wizard.tabs.environment": "", "installer_messages.environment.wizard.tabs.database": "", "installer_messages.environment.wizard.tabs.application": "", "installer_messages.environment.wizard.form.app_name_label": "", "installer_messages.environment.wizard.form.app_url_label": "", "installer_messages.environment.wizard.form.buttons.setup_application": "", "installer_messages.final.migration": "", "installer_messages.final.console": "", "installer_messages.final.env": "", "installer_messages.welcome.title": "", "There are two apples": ""}