{"__meta": {"id": "X2cd1f9b22b2b25382432b373d2eedb76", "datetime": "2025-07-07 21:28:47", "utime": 1751923727.527851, "method": "GET", "uri": "/mazar/public/custom-css", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751923726.513882, "end": 1751923727.52792, "duration": 1.0140380859375, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1751923726.513882, "relative_start": 0, "end": 1751923727.453466, "relative_end": 1751923727.453466, "duration": 0.9395840167999268, "duration_str": "940ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751923727.453504, "relative_start": 0.939622163772583, "end": 1751923727.527926, "relative_end": 5.9604644775390625e-06, "duration": 0.07442188262939453, "duration_str": "74.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 4686496, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FControllers%2FStyleController.php&line=9\" onclick=\"\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fvaLd7mqR2GdTsywFJ4CUvyBeElAH21zDSguYalh", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-1087395170 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1087395170\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-20920348 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-20920348\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-19350311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-19350311\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1841343328 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/mazar/public/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IlZnZncrVWtRV2VQTGNaSVdxbUE0c1E9PSIsInZhbHVlIjoiLzl5VldxSFR1MXcvM1pBS3NnZi90QThqOXdjNlRIR0ZwSFRuZUZJKzMxZkt3UTd6SkEvVmwxV1NwS256aHhVeUtIMVpYSjl4c01HZ1NTZEhOU1ZhQ0VlTDRZMTVXSXkvY3Rrbk9PdWJUbytJallZQmE5MjdnRjA3TFo0SFczU0EiLCJtYWMiOiI2OTkwNmFiOTY1YTMyMmQ1MDFkY2ZmOWU1OTFlMTQ1ZjQ0MzllYTllMzJkYTVhY2M4ZGNmYjJiZWE3MGYyMDQ4IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6Im5PclVYU1M1cDNldzNTZjd2eDFSRXc9PSIsInZhbHVlIjoiMVJPWVNTSWhmZE42cFB5ZHVqVEZsN2RDMFdxYm1laVV4R2I3dUErbTFrcEZhdmxQYW9wYXUxVlhpWURkN2QrM01GUW9COG1aQ3BUNEdYM0piUXZTTFB5a0xUQnZOaExGcEVrRVlJQ0xuelJpdmtkMGZRRmtjWnpuclBhVmF2TVMiLCJtYWMiOiJmOTJlNzA0M2Y3YWRlYWEyYzhhMzZkYmE5MmRlNzE5YmFmNTVmNTJlMzU2N2NjMjc4YjcyNWNkY2IzMDk5OTQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841343328\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fvaLd7mqR2GdTsywFJ4CUvyBeElAH21zDSguYalh</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wskoxbfRzLXVdH319d5WARiJUV5yxWxb6WNHoPGX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-61969548 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:28:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IllBWW9wVVBRYnc5VUY4WlUwV1d2SkE9PSIsInZhbHVlIjoiSHM4bUYrdGxRWHkrWWk4T2FRN1JEcEViT0lVWjdPdjhIcXVEMXNYMUt5OVJhQjJaYkJCdmVTZDNQbmtDaEdLSnViT1Z0aEFmdFVhcm5sVXNqN3VkVEJ5Tnd4U3RwUHdROU41OXZ0TklkbWJCSDV2VElsZHcxSjQ5RWlFRUQzMHgiLCJtYWMiOiJiYTU4M2NmMTU2OGI1MmIwZWQ4NTZiY2JjMDIwYWI2YzljNzlhMjYzZGU0YTA4N2Q5NDA1NTA0NTRmZTM4ZDdiIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:28:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjRPM2VocnJPSFhhaTRJdEt5TnhFcUE9PSIsInZhbHVlIjoibVZkUzZaQVhLMy9HTmhlNUNlektSNzV3RmZZRjRMM3g4a3R5dTh2Mkp3RVlWaXNSVXFjdWdlRXE0TFBTNGM1dEp0Uk5ramYySkwzUXpCYy9xQ09iNmEvRDJENDc4UjZ6S0IvdGs1TEs4aHhGRzVuT21hYXA2ZUpYMWUxZHZMVzUiLCJtYWMiOiJhNDY3NTdiZTg1Nzk1NmY2NDMzYTIwNWY4ZjU5OGY1MDY3NWQ1NDdjMjU5MDlmNWM2YmVjNDNmZGU0NWY0MGI2IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:28:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IllBWW9wVVBRYnc5VUY4WlUwV1d2SkE9PSIsInZhbHVlIjoiSHM4bUYrdGxRWHkrWWk4T2FRN1JEcEViT0lVWjdPdjhIcXVEMXNYMUt5OVJhQjJaYkJCdmVTZDNQbmtDaEdLSnViT1Z0aEFmdFVhcm5sVXNqN3VkVEJ5Tnd4U3RwUHdROU41OXZ0TklkbWJCSDV2VElsZHcxSjQ5RWlFRUQzMHgiLCJtYWMiOiJiYTU4M2NmMTU2OGI1MmIwZWQ4NTZiY2JjMDIwYWI2YzljNzlhMjYzZGU0YTA4N2Q5NDA1NTA0NTRmZTM4ZDdiIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:28:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjRPM2VocnJPSFhhaTRJdEt5TnhFcUE9PSIsInZhbHVlIjoibVZkUzZaQVhLMy9HTmhlNUNlektSNzV3RmZZRjRMM3g4a3R5dTh2Mkp3RVlWaXNSVXFjdWdlRXE0TFBTNGM1dEp0Uk5ramYySkwzUXpCYy9xQ09iNmEvRDJENDc4UjZ6S0IvdGs1TEs4aHhGRzVuT21hYXA2ZUpYMWUxZHZMVzUiLCJtYWMiOiJhNDY3NTdiZTg1Nzk1NmY2NDMzYTIwNWY4ZjU5OGY1MDY3NWQ1NDdjMjU5MDlmNWM2YmVjNDNmZGU0NWY0MGI2IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:28:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61969548\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1380280610 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fvaLd7mqR2GdTsywFJ4CUvyBeElAH21zDSguYalh</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/mazar/public/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380280610\", {\"maxDepth\":0})</script>\n"}}