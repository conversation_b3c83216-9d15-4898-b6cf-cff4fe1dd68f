{"__meta": {"id": "X2fc9b1e7d947b5415722cd26c41913d6", "datetime": "2025-07-07 21:39:30", "utime": **********.718933, "method": "GET", "uri": "/mazar/public/admin/module/vendor/payout", "ip": "::1"}, "php": {"version": "8.2.13", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751924369.629997, "end": **********.718959, "duration": 1.0889620780944824, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1751924369.629997, "relative_start": 0, "end": 1751924369.976627, "relative_end": 1751924369.976627, "duration": 0.3466300964355469, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751924369.976645, "relative_start": 0.3466479778289795, "end": **********.718962, "relative_end": 2.86102294921875e-06, "duration": 0.7423169612884521, "duration_str": "742ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 8980728, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 13, "templates": [{"name": "1x Vendor::admin.payouts.index", "param_count": null, "params": [], "start": **********.081191, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Vendor/Views/admin/payouts/index.blade.phpVendor::admin.payouts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FViews%2Fadmin%2Fpayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Vendor::admin.payouts.index"}, {"name": "1x admin.message", "param_count": null, "params": [], "start": **********.288319, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/message.blade.phpadmin.message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.message"}, {"name": "1x pagination::tailwind", "param_count": null, "params": [], "start": **********.291844, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/vendor/pagination/tailwind.blade.phppagination::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fvendor%2Fpagination%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "pagination::tailwind"}, {"name": "1x vendor.pagination.default", "param_count": null, "params": [], "start": **********.292643, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/vendor/pagination/default.blade.phpvendor.pagination.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.pagination.default"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.295413, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x Layout::admin.app", "param_count": null, "params": [], "start": **********.296376, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/app.blade.phpLayout::admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.app"}, {"name": "1x Layout::admin.parts.global-script", "param_count": null, "params": [], "start": **********.299799, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/global-script.blade.phpLayout::admin.parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.global-script"}, {"name": "1x Layout::admin.parts.header", "param_count": null, "params": [], "start": **********.316225, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.phpLayout::admin.parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.header"}, {"name": "1x Layout::admin.parts.sidebar", "param_count": null, "params": [], "start": **********.340657, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.phpLayout::admin.parts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.sidebar"}, {"name": "1x Layout::admin.parts.bc", "param_count": null, "params": [], "start": **********.702325, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/bc.blade.phpLayout::admin.parts.bc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fbc.blade.php&line=1", "ajax": false, "filename": "bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.bc"}, {"name": "1x Media::browser", "param_count": null, "params": [], "start": **********.703618, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Media/Views/browser.blade.phpMedia::browser", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FViews%2Fbrowser.blade.php&line=1", "ajax": false, "filename": "browser.blade.php", "line": "?"}, "render_count": 1, "name_original": "Media::browser"}, {"name": "1x Pro::admin.upgrade-modal", "param_count": null, "params": [], "start": **********.706346, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\app\\Pro/Views/admin/upgrade-modal.blade.phpPro::admin.upgrade-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FPro%2FViews%2Fadmin%2Fupgrade-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Pro::admin.upgrade-modal"}, {"name": "1x Ai::frontend.text-generate", "param_count": null, "params": [], "start": **********.70775, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\pro\\Ai/Views/frontend/text-generate.blade.phpAi::frontend.text-generate", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fpro%2FAi%2FViews%2Ffrontend%2Ftext-generate.blade.php&line=1", "ajax": false, "filename": "text-generate.blade.php", "line": "?"}, "render_count": 1, "name_original": "Ai::frontend.text-generate"}]}, "route": {"uri": "GET admin/module/vendor/payout", "middleware": "web, dashboard", "controller": "Modules\\Vendor\\Admin\\PayoutController@index", "namespace": "Modules\\Vendor\\Admin", "prefix": "admin/module/vendor/payout", "where": [], "as": "vendor.admin.payout.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FAdmin%2FPayoutController.php&line=24\" onclick=\"\">modules/Vendor/Admin/PayoutController.php:24-52</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.020419999999999997, "accumulated_duration_str": "20.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.039811, "duration": 0.01001, "duration_str": "10.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.053925, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'vendor_payout_view')", "type": "query", "params": [], "bindings": ["1", "vendor_payout_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.06577, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_payouts`", "type": "query", "params": [], "bindings": [], "hints": ["The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Vendor/Admin/PayoutController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\Admin\\PayoutController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.074327, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PayoutController.php:41", "source": "modules/Vendor/Admin/PayoutController.php:41", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FAdmin%2FPayoutController.php&line=41", "ajax": false, "filename": "PayoutController.php", "line": "41"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.318449, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3239279, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.331114, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 132}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.335273, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_news` where `status` = 'pending' and `core_news`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/News/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\ModuleProvider.php", "line": 31}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4012928, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:31", "source": "modules/News/ModuleProvider.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModuleProvider.php&line=31", "ajax": false, "filename": "ModuleProvider.php", "line": "31"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `users` where `verify_submit_status` in ('new', 'partial') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["new", "partial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 407}, {"index": 17, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 47}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.4061031, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "User.php:407", "source": "app/User.php:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=407", "ajax": false, "filename": "User.php", "line": "407"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `user_upgrade_request` where `status` = 'pending' and exists (select * from `users` where `user_upgrade_request`.`user_id` = `users`.`id` and `users`.`deleted_at` is null) and `user_upgrade_request`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 48}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.410448, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:48", "source": "modules/User/ModuleProvider.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=48", "ajax": false, "filename": "ModuleProvider.php", "line": "48"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_booking_payments` where `object_model` = 'plan' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["plan", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 93}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.416941, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:93", "source": "modules/User/ModuleProvider.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=93", "ajax": false, "filename": "ModuleProvider.php", "line": "93"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_booking_payments` where `object_model` = 'wallet_deposit' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["wallet_deposit", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Wallet/DepositPayment.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Wallet\\DepositPayment.php", "line": 15}, {"index": 17, "namespace": null, "name": "modules/Report/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Report\\ModuleProvider.php", "line": 18}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.421426, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DepositPayment.php:15", "source": "modules/User/Models/Wallet/DepositPayment.php:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FWallet%2FDepositPayment.php&line=15", "ajax": false, "filename": "DepositPayment.php", "line": "15"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_payouts` where `status` = 'initial'", "type": "query", "params": [], "bindings": ["initial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Vendor/Models/VendorPayout.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\Models\\VendorPayout.php", "line": 63}, {"index": 17, "namespace": null, "name": "modules/Vendor/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\ModuleProvider.php", "line": 27}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 229}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 240}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.426332, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "VendorPayout.php:63", "source": "modules/Vendor/Models/VendorPayout.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FModels%2FVendorPayout.php&line=63", "ajax": false, "filename": "VendorPayout.php", "line": "63"}, "connection": "mazar_travel"}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS", "_previous": "array:1 [\n  \"url\" => \"http://localhost/mazar/public/admin/module/vendor/payout\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "bc_current_currency": "", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/module/vendor/payout", "status_code": "<pre class=sf-dump id=sf-dump-2022785345 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2022785345\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1840254702 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1840254702\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-295387267 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-295387267\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1344566997 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://localhost/mazar/public/admin/module/language</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6ImVqaXo3VkZaakpsZFppNDd5dlVkdHc9PSIsInZhbHVlIjoid2pMV1VQcFcxa0tFWlpUVGFjRWlWeEdnOFRiYlF0OGNQZStXZ1B1bTZtT0I4QWVsZWt4QnRNSUZncmY2WEJveTlVMVFBaUcrdzBZUnIvS2tNQWE3cWxkd20rdGVlaElyNVVmNDJGMFNHOWR0bGtUWXpwb1BxZ05iS2JLQWovby8iLCJtYWMiOiJjZmZiMTg2OWUyN2MyYTc2ZmU1Y2RjZWVmNDJiNGNlZWRkYTkyZTFlYTJmODA3YTMxODNiYzk0OTUwODJhMzgzIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6InM2bXFiN0FMTXdFU2NxcVVsSk5SM2c9PSIsInZhbHVlIjoiMUs4VmtpYjRmQTV5VVZaM2pKbE9JbDdhTWZJNEZiQ3FhV2RlTXdwOEFTMEUvU3Fhb3NaeXJ3dTBqRFRyUmlpMHVXMklaaFcwNEdqNy9RdE9ubmFteFRKOWVNVWdmMGtSU05NUXhESFZNYVR0S01CWE5xQ3M5RmZRRGJOWHZEVlAiLCJtYWMiOiIwYTFmMjM2YzY1N2Y3MjE1NjNmMjVmNmI2NjQyMDczNzQwZjQwYjgwMTc3OGYyOGRhMTg0OTA2NDdkMDNjOWYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344566997\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1345377746 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EL5tyKZMFAXgFoH4oVl3aDpHOSef0nw7JBkJdvjn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345377746\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1246037 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 07 Jul 2025 21:39:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9oTjVQNGxhVUNQQzFBaXNnT3dZN0E9PSIsInZhbHVlIjoiWmx6bGdnc3k4MHVoMEJVRzZLdlBMRGlhaGxRa1UxTmJvTHZNVytrZzlIS3JkcnNqeUswd21zVmkwaTgzaVVhZ1FGYXV2YTZrZHpGMnpjQUpmTEsyU2JoNC8wbzU3NThSMk93alJJUVl5WGNuQVlHcU40RlFBWW1OMVo4R09UZDciLCJtYWMiOiJhNGUzNjE5MWMyYzhiMDk3M2NiYzRjZmZkZGQwNTg1NzA5NDg0ZTVmM2FjZjVlZGVkMzczZDRiZTE3YmZhZjc0IiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:39:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6InRTRGIrT1BMK3dwcy9sNE5iaWUyN1E9PSIsInZhbHVlIjoiOGVldkM0RGF3V3NUTjROaGpDQW4zZkhtcG11TWtJZjhmRXRSZ2tJNlhhZjkxdkJVNFFBa1FnQkdsMHNZcmFuRHNtK1pOdXJneXNKTWc3Rlc0R0xYWU90dHZpcEd1MTNwUDdoTlJ4K1dUTWRkYmcwMmJ5QVJreG1mRDFCV29wL0UiLCJtYWMiOiI1MjZjY2E2NTljZDc4MzM3ZmQ0ZjQ2ZjhiMGNhZjA3ZDkwNDA5NWIwMDczMWRlNTFmNTUxODcxOGVjNzA4ZDAwIiwidGFnIjoiIn0%3D; expires=Mon, 07 Jul 2025 23:39:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9oTjVQNGxhVUNQQzFBaXNnT3dZN0E9PSIsInZhbHVlIjoiWmx6bGdnc3k4MHVoMEJVRzZLdlBMRGlhaGxRa1UxTmJvTHZNVytrZzlIS3JkcnNqeUswd21zVmkwaTgzaVVhZ1FGYXV2YTZrZHpGMnpjQUpmTEsyU2JoNC8wbzU3NThSMk93alJJUVl5WGNuQVlHcU40RlFBWW1OMVo4R09UZDciLCJtYWMiOiJhNGUzNjE5MWMyYzhiMDk3M2NiYzRjZmZkZGQwNTg1NzA5NDg0ZTVmM2FjZjVlZGVkMzczZDRiZTE3YmZhZjc0IiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:39:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6InRTRGIrT1BMK3dwcy9sNE5iaWUyN1E9PSIsInZhbHVlIjoiOGVldkM0RGF3V3NUTjROaGpDQW4zZkhtcG11TWtJZjhmRXRSZ2tJNlhhZjkxdkJVNFFBa1FnQkdsMHNZcmFuRHNtK1pOdXJneXNKTWc3Rlc0R0xYWU90dHZpcEd1MTNwUDdoTlJ4K1dUTWRkYmcwMmJ5QVJreG1mRDFCV29wL0UiLCJtYWMiOiI1MjZjY2E2NTljZDc4MzM3ZmQ0ZjQ2ZjhiMGNhZjA3ZDkwNDA5NWIwMDczMWRlNTFmNTUxODcxOGVjNzA4ZDAwIiwidGFnIjoiIn0%3D; expires=Mon, 07-Jul-2025 23:39:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246037\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1901268768 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MCqFSY3HQRbtFepILDsJI6xnLf7GIw4QfYAOWcdS</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://localhost/mazar/public/admin/module/vendor/payout</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901268768\", {\"maxDepth\":0})</script>\n"}}